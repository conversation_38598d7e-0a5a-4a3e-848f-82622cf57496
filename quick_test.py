#!/usr/bin/env python3
"""
Quick Test Script for Udemy Video Downloader
اختبار سريع لمحمل فيديوهات Udemy
"""

import sys
import os
import json
from udemy_video_downloader import UdemyVideoDownloader
from drm_analyzer import DRMA<PERSON>y<PERSON>

def test_url_analysis():
    """اختبار تحليل الرابط"""
    print("🧪 اختبار تحليل الرابط...")
    
    mpd_url = "https://www.udemy.com/assets/39324048/encrypted-files/out/v1/ab6818eaa27c464ab8afce6ecf1f499b/3e691b0da4414b83836363226ac161e9/ecd8798041b7474bb05daed9aae97b93/index.mpd?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************.3SEE4F5QZACFkw4xKNJOfiF23a3XlazMVuPG60X836g&provider=cloudfront&v=1&CMCD=ot%3Dm%2Csf%3Dd%2Csid%3D%222be19078-2228-4b1d-b6ce-50569e866453%22%2Csu"
    
    downloader = UdemyVideoDownloader()
    url_info = downloader.analyze_mpd_url(mpd_url)
    
    print("📊 نتائج التحليل:")
    for key, value in url_info.items():
        print(f"   {key}: {value}")
    
    return url_info['asset_id'] is not None

def test_mpd_fetch():
    """اختبار جلب MPD"""
    print("\n🧪 اختبار جلب MPD...")
    
    mpd_url = "https://www.udemy.com/assets/39324048/encrypted-files/out/v1/ab6818eaa27c464ab8afce6ecf1f499b/3e691b0da4414b83836363226ac161e9/ecd8798041b7474bb05daed9aae97b93/index.mpd?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************.3SEE4F5QZACFkw4xKNJOfiF23a3XlazMVuPG60X836g&provider=cloudfront&v=1&CMCD=ot%3Dm%2Csf%3Dd%2Csid%3D%222be19078-2228-4b1d-b6ce-50569e866453%22%2Csu"
    
    downloader = UdemyVideoDownloader()
    mpd_content = downloader.fetch_mpd_content(mpd_url)
    
    if mpd_content:
        print(f"✅ تم جلب MPD ({len(mpd_content)} characters)")
        
        # حفظ للفحص
        with open("test_mpd.xml", "w", encoding="utf-8") as f:
            f.write(mpd_content)
        print("💾 تم حفظ MPD في test_mpd.xml")
        
        return True
    else:
        print("❌ فشل في جلب MPD")
        return False

def test_drm_analysis():
    """اختبار تحليل DRM"""
    print("\n🧪 اختبار تحليل DRM...")
    
    # PSSH من المثال السابق
    pssh_b64 = "AAADxHBzc2gAAAAAmgTweZhAQoarkuZb4IhflQAAA6SkAwAAAQABAJoDPABXAFIATQBIAEUAQQBEAEUAUgAgAHgAbQBsAG4AcwA9ACIAaAB0AHQAcAA6AC8ALwBzAGMAaABlAG0AYQBzAC4AbQBpAGMAcgBvAHMAbwBmAHQALgBjAG8AbQAvAEQAUgBNAC8AMgAwADAANwAvADAAMwAvAFAAbABhAHkAUgBlAGEAZAB5AEgAZQBhAGQAZQByACIAIAB2AGUAcgBzAGkAbwBuAD0AIgA0AC4AMAAuADAALgAwACIAPgA8AEQAQQBUAEEAPgA8AFAAUgBPAFQARQBDAFQASQBOAEYATwA+ADwASwBFAFkATABFAE4APgAxADYAPAAvAEsARQBZAEwARQBOAD4APABBAEwARwBJAEQAPgBBAEUAUwBDAFQAUgA8AC8AQQBMAEcASQBEAD4APAAvAFAAUgBPAFQARQBDAFQASQBOAEYATwA+ADwASwBJAEQAPgAvAHIANgBIAEgASAB2AGkAbABFADIAWQBRAGkAKwBEAC8AKwBCAGIAQwBnAD0APQA8AC8ASwBJAEQAPgA8AEwAQQBfAFUAUgBMAD4AaAB0AHQAcABzADoALwAvAHAAbABhAHkAcgBlAGEAZAB5AC4AawBlAHkAbwBzAC4AYwBvAG0ALwBhAHAAaQAvAHYANAAvAGcAZQB0AEwAaQBjAGUAbgBzAGUAPAAvAEwAQQBfAFUAUgBMAD4APABEAFMAXwBJAEQAPgBWAGwAUgA3AEkAZABzAEkASgBFAHUAUgBkADAANgBMAGEAcQBzADIAagB3AD0APQA8AC8ARABTAF8ASQBEAD4APABDAFUAUwBUAE8ATQBBAFQAVABSAEkAQgBVAFQARQBTACAAeABtAGwAbgBzAD0AIgAiAD4APABDAEkARAA+AC8AcgA2AEgASABIAHYAaQBsAEUAMgBZAFEAaQArAEQALwArAEIAYgBDAGcAPQA9ADwALwBDAEkARAA+ADwARABSAE0AVABZAFAARQA+AHMAbQBvAG8AdABoADwALwBEAFIATQBUAFkAUABFAD4APAAvAEMAVQBTAFQATwBNAEEAVABUAFIASQBCAFUAVABFAFMAPgA8AEMASABFAEMASwBTAFUATQA+AGwAMgBOADAAMQBFAHgAVABaAFUAdwA9ADwALwBDAEgARQBDAEsAUwBVAE0APgA8AC8ARABBAFQAQQA+ADwALwBXAFIATQBIAEUAQQBEAEUAUgA+AAAAAFdwc3NoAAAAAO3vi6l51krOo8gn3NUdIe0AAAA3CAESEByHvv7ie02UmEIvg//gWwoaC2J1eWRybWtleW9zIhAch77+4ntNlJhCL4P/4FsKKgJIRA=="
    
    analyzer = DRMAnalyzer()
    analysis = analyzer.analyze_pssh(pssh_b64)
    
    if analysis:
        print("✅ تم تحليل PSSH بنجاح")
        print(f"📊 حجم البيانات: {analysis['size']} bytes")
        print(f"🔐 System ID: {analysis.get('system_id', 'غير متاح')}")
        print(f"🔑 عدد Key IDs: {len(analysis.get('key_ids', []))}")
        
        return True
    else:
        print("❌ فشل في تحليل PSSH")
        return False

def test_segment_download():
    """اختبار تحميل segment واحد"""
    print("\n🧪 اختبار تحميل segment...")
    
    # إنشاء segment تجريبي
    test_segment = {
        'number': 1,
        'url': 'https://httpbin.org/bytes/1024',  # URL تجريبي
        'type': 'video/mp4',
        'bandwidth': '1000000'
    }
    
    downloader = UdemyVideoDownloader()
    result = downloader.download_segment(test_segment, "test_downloads")
    
    if result['success']:
        print(f"✅ تم تحميل segment ({result['size']} bytes)")
        
        # حذف الملف التجريبي
        try:
            os.remove(result['file'])
            os.rmdir("test_downloads")
        except:
            pass
        
        return True
    else:
        print(f"❌ فشل تحميل segment: {result['error']}")
        return False

def run_all_tests():
    """تشغيل جميع الاختبارات"""
    print("🧪 بدء الاختبارات الشاملة...")
    print("=" * 50)
    
    tests = [
        ("تحليل الرابط", test_url_analysis),
        ("جلب MPD", test_mpd_fetch),
        ("تحليل DRM", test_drm_analysis),
        ("تحميل Segment", test_segment_download)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
            
            if result:
                print(f"✅ {test_name}: نجح")
            else:
                print(f"❌ {test_name}: فشل")
                
        except Exception as e:
            print(f"💥 {test_name}: خطأ - {e}")
            results.append((test_name, False))
        
        print("-" * 30)
    
    # ملخص النتائج
    print("\n📊 ملخص النتائج:")
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    print(f"✅ نجح: {passed}/{total}")
    print(f"❌ فشل: {total - passed}/{total}")
    print(f"📈 معدل النجاح: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print("\n🎉 جميع الاختبارات نجحت!")
        return True
    else:
        print("\n⚠️ بعض الاختبارات فشلت")
        return False

def main():
    """الدالة الرئيسية"""
    print("🧪 Udemy Video Downloader - Quick Test")
    print("=" * 40)
    
    # التحقق من المكتبات
    try:
        import requests
        import xml.etree.ElementTree as ET
        print("✅ المكتبات المطلوبة متاحة")
    except ImportError as e:
        print(f"❌ مكتبة مفقودة: {e}")
        print("يرجى تشغيل: pip install -r requirements.txt")
        return
    
    # تشغيل الاختبارات
    success = run_all_tests()
    
    if success:
        print("\n🚀 النظام جاهز للاستخدام!")
        print("يمكنك الآن تشغيل: python udemy_video_downloader.py")
    else:
        print("\n🔧 يرجى إصلاح المشاكل قبل الاستخدام")

if __name__ == "__main__":
    main()
