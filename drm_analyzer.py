#!/usr/bin/env python3
"""
DRM Analyzer for Udemy Videos
محلل DRM لفيديوهات Udemy
"""

import requests
import json
import base64
import xml.etree.ElementTree as ET
from urllib.parse import urlparse
import binascii
import struct

class DRMAnalyzer:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': '*/*',
            'Accept-Language': 'en-US,en;q=0.9',
        })
    
    def analyze_pssh(self, pssh_b64):
        """تحليل PSSH بتفصيل أكثر"""
        print("🔐 تحليل PSSH...")
        
        try:
            # فك تشفير Base64
            pssh_data = base64.b64decode(pssh_b64)
            
            # تحليل PSSH box structure
            analysis = {
                'size': len(pssh_data),
                'hex': binascii.hexlify(pssh_data).decode(),
                'system_id': None,
                'key_ids': [],
                'data': None
            }
            
            # البحث عن PSSH header
            if pssh_data[:4] == b'pssh':
                print("❌ PSSH header غير صحيح")
                return analysis
            
            # قراءة حجم PSSH box
            if len(pssh_data) >= 8:
                box_size = struct.unpack('>I', pssh_data[:4])[0]
                box_type = pssh_data[4:8]
                
                if box_type == b'pssh':
                    print(f"✅ وجد PSSH box (حجم: {box_size})")
                    
                    # قراءة version و flags
                    if len(pssh_data) >= 12:
                        version_flags = struct.unpack('>I', pssh_data[8:12])[0]
                        version = (version_flags >> 24) & 0xFF
                        
                        print(f"📋 PSSH Version: {version}")
                        
                        # قراءة System ID (16 bytes)
                        if len(pssh_data) >= 28:
                            system_id = pssh_data[12:28]
                            analysis['system_id'] = binascii.hexlify(system_id).decode()
                            
                            # تحديد نوع DRM
                            drm_type = self.identify_drm_system(system_id)
                            print(f"🔍 نوع DRM: {drm_type}")
                            
                            # قراءة بيانات إضافية حسب الإصدار
                            offset = 28
                            
                            if version > 0:
                                # قراءة عدد Key IDs
                                if len(pssh_data) >= offset + 4:
                                    kid_count = struct.unpack('>I', pssh_data[offset:offset+4])[0]
                                    offset += 4
                                    
                                    print(f"🔑 عدد Key IDs: {kid_count}")
                                    
                                    # قراءة Key IDs
                                    for i in range(kid_count):
                                        if len(pssh_data) >= offset + 16:
                                            key_id = pssh_data[offset:offset+16]
                                            analysis['key_ids'].append(binascii.hexlify(key_id).decode())
                                            offset += 16
                            
                            # قراءة Data
                            if len(pssh_data) >= offset + 4:
                                data_size = struct.unpack('>I', pssh_data[offset:offset+4])[0]
                                offset += 4
                                
                                if len(pssh_data) >= offset + data_size:
                                    data = pssh_data[offset:offset+data_size]
                                    analysis['data'] = data
                                    
                                    # محاولة تحليل البيانات كـ XML
                                    try:
                                        data_str = data.decode('utf-16le')
                                        if '<WRMHEADER' in data_str:
                                            print("📄 وجد PlayReady Header")
                                            analysis['playready_header'] = self.parse_playready_header(data_str)
                                    except:
                                        pass
            
            return analysis
            
        except Exception as e:
            print(f"❌ خطأ في تحليل PSSH: {e}")
            return None
    
    def identify_drm_system(self, system_id):
        """تحديد نوع نظام DRM"""
        system_id_hex = binascii.hexlify(system_id).decode()
        
        drm_systems = {
            '9a04f0799840428aab92e65be0885f95': 'PlayReady',
            'edef8ba979d64acea3c827dcd51d21ed': 'Widevine',
            '94ce86fb07ff4f43adb893d2fa968ca2': 'FairPlay',
            '1077efecc0b24d02ace33c1e52e2fb4b': 'ClearKey'
        }
        
        return drm_systems.get(system_id_hex, f'Unknown ({system_id_hex})')
    
    def parse_playready_header(self, header_xml):
        """تحليل PlayReady header"""
        try:
            # إزالة null characters
            clean_xml = header_xml.replace('\x00', '')
            
            # البحث عن معلومات مهمة
            info = {}
            
            if '<KID>' in clean_xml:
                kid_start = clean_xml.find('<KID>') + 5
                kid_end = clean_xml.find('</KID>')
                if kid_end > kid_start:
                    info['key_id'] = clean_xml[kid_start:kid_end]
            
            if '<LA_URL>' in clean_xml:
                url_start = clean_xml.find('<LA_URL>') + 8
                url_end = clean_xml.find('</LA_URL>')
                if url_end > url_start:
                    info['license_url'] = clean_xml[url_start:url_end]
            
            if '<DS_ID>' in clean_xml:
                ds_start = clean_xml.find('<DS_ID>') + 7
                ds_end = clean_xml.find('</DS_ID>')
                if ds_end > ds_start:
                    info['ds_id'] = clean_xml[ds_start:ds_end]
            
            return info
            
        except Exception as e:
            print(f"❌ خطأ في تحليل PlayReady header: {e}")
            return {}
    
    def test_license_server(self, license_url, pssh_data):
        """اختبار خادم الترخيص"""
        print(f"🌐 اختبار خادم الترخيص: {license_url}")
        
        try:
            # إنشاء license request
            challenge_data = base64.b64encode(pssh_data).decode()
            
            headers = {
                'Content-Type': 'application/octet-stream',
                'SOAPAction': '"http://schemas.microsoft.com/DRM/2007/03/protocols/AcquireLicense"'
            }
            
            response = self.session.post(
                license_url,
                data=challenge_data,
                headers=headers,
                timeout=10
            )
            
            print(f"📊 رد الخادم: {response.status_code}")
            print(f"📊 حجم الرد: {len(response.content)} bytes")
            
            if response.status_code == 200:
                print("✅ الخادم يستجيب")
                return True
            else:
                print(f"⚠️ الخادم رد بخطأ: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في الاتصال بالخادم: {e}")
            return False
    
    def analyze_mpd_drm(self, mpd_url):
        """تحليل DRM في ملف MPD"""
        print("🔍 تحليل DRM في MPD...")
        
        try:
            response = self.session.get(mpd_url, timeout=30)
            response.raise_for_status()
            
            root = ET.fromstring(response.text)
            
            # البحث عن ContentProtection
            namespaces = {'mpd': 'urn:mpeg:dash:schema:mpd:2011'}
            
            drm_info = []
            
            for content_protection in root.findall('.//mpd:ContentProtection', namespaces):
                scheme_id = content_protection.get('schemeIdUri', '')
                value = content_protection.get('value', '')
                
                info = {
                    'scheme_id': scheme_id,
                    'value': value,
                    'pssh': None
                }
                
                # البحث عن PSSH
                pssh_element = content_protection.find('pssh')
                if pssh_element is not None and pssh_element.text:
                    info['pssh'] = pssh_element.text.strip()
                    
                    # تحليل PSSH
                    pssh_analysis = self.analyze_pssh(info['pssh'])
                    if pssh_analysis:
                        info['pssh_analysis'] = pssh_analysis
                        
                        # اختبار license server إذا وجد
                        if 'playready_header' in pssh_analysis:
                            pr_header = pssh_analysis['playready_header']
                            if 'license_url' in pr_header:
                                info['license_test'] = self.test_license_server(
                                    pr_header['license_url'],
                                    base64.b64decode(info['pssh'])
                                )
                
                drm_info.append(info)
            
            return drm_info
            
        except Exception as e:
            print(f"❌ خطأ في تحليل MPD: {e}")
            return []
    
    def generate_report(self, mpd_url, output_file="drm_analysis_report.json"):
        """إنشاء تقرير تحليل DRM"""
        print("📋 إنشاء تقرير تحليل DRM...")
        
        # تحليل URL
        parsed_url = urlparse(mpd_url)
        
        report = {
            'url': mpd_url,
            'domain': parsed_url.netloc,
            'timestamp': __import__('datetime').datetime.now().isoformat(),
            'drm_analysis': self.analyze_mpd_drm(mpd_url)
        }
        
        # حفظ التقرير
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"💾 تم حفظ التقرير: {output_file}")
        
        # طباعة ملخص
        print("\n📊 ملخص التحليل:")
        print(f"🔗 الرابط: {mpd_url}")
        print(f"🔐 عدد أنظمة DRM: {len(report['drm_analysis'])}")
        
        for i, drm in enumerate(report['drm_analysis'], 1):
            print(f"\n🔐 نظام DRM #{i}:")
            print(f"   Scheme: {drm['scheme_id']}")
            if 'pssh_analysis' in drm:
                pssh = drm['pssh_analysis']
                if 'system_id' in pssh:
                    drm_type = self.identify_drm_system(bytes.fromhex(pssh['system_id']))
                    print(f"   Type: {drm_type}")
                if 'playready_header' in pssh:
                    pr = pssh['playready_header']
                    if 'license_url' in pr:
                        print(f"   License URL: {pr['license_url']}")
        
        return report

def main():
    """الدالة الرئيسية"""
    # رابط الفيديو
    mpd_url = "https://www.udemy.com/assets/39324048/encrypted-files/out/v1/ab6818eaa27c464ab8afce6ecf1f499b/3e691b0da4414b83836363226ac161e9/ecd8798041b7474bb05daed9aae97b93/index.mpd?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************.3SEE4F5QZACFkw4xKNJOfiF23a3XlazMVuPG60X836g&provider=cloudfront&v=1&CMCD=ot%3Dm%2Csf%3Dd%2Csid%3D%222be19078-2228-4b1d-b6ce-50569e866453%22%2Csu"
    
    analyzer = DRMAnalyzer()
    report = analyzer.generate_report(mpd_url)
    
    print("\n✅ تم إكمال تحليل DRM!")

if __name__ == "__main__":
    main()
