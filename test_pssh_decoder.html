<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PSSH Decoder Test - اختبار فك تشفير PSSH</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .content {
            padding: 30px;
        }
        .input-section {
            margin-bottom: 30px;
        }
        .input-section label {
            display: block;
            margin-bottom: 10px;
            font-weight: bold;
            color: #333;
        }
        .input-section textarea {
            width: 100%;
            height: 120px;
            padding: 15px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            resize: vertical;
            box-sizing: border-box;
        }
        .button {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: transform 0.2s;
        }
        .button:hover {
            transform: translateY(-2px);
        }
        .result-section {
            margin-top: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }
        .result-item {
            margin-bottom: 15px;
            padding: 10px;
            background: white;
            border-radius: 5px;
            border: 1px solid #e9ecef;
        }
        .result-item strong {
            color: #495057;
        }
        .hex-output {
            font-family: 'Courier New', monospace;
            background: #2d3748;
            color: #68d391;
            padding: 15px;
            border-radius: 5px;
            word-break: break-all;
            margin-top: 10px;
        }
        .error {
            background: #fed7d7;
            color: #c53030;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #e53e3e;
        }
        .success {
            background: #c6f6d5;
            color: #22543d;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #38a169;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔐 PSSH Decoder Test</h1>
            <p>اختبار فك تشفير PSSH من إضافة UdemyFetcher</p>
        </div>
        
        <div class="content">
            <div class="input-section">
                <label for="psshInput">أدخل PSSH (Base64):</label>
                <textarea id="psshInput" placeholder="مثال: AAADxHBzc2gAAAAAmgTweZhAQoarkuZb4IhflQAAA6SkAwAAAQABAJoD...">AAADxHBzc2gAAAAAmgTweZhAQoarkuZb4IhflQAAA6SkAwAAAQABAJoDPABXAFIATQBIAEUAQQBEAEUAUgAgAHgAbQBsAG4AcwA9ACIAaAB0AHQAcAA6AC8ALwBzAGMAaABlAG0AYQBzAC4AbQBpAGMAcgBvAHMAbwBmAHQALgBjAG8AbQAvAEQAUgBNAC8AMgAwADAANwAvADAAMwAvAFAAbABhAHkAUgBlAGEAZAB5AEgAZQBhAGQAZQByACIAIAB2AGUAcgBzAGkAbwBuAD0AIgA0AC4AMAAuADAALgAwACIAPgA8AEQAQQBUAEEAPgA8AFAAUgBPAFQARQBDAFQASQBOAEYATwA+ADwASwBFAFkATABFAE4APgAxADYAPAAvAEsARQBZAEwARQBOAD4APABBAEwARwBJAEQAPgBBAEUAUwBDAFQAUgA8AC8AQQBMAEcASQBEAD4APAAvAFAAUgBPAFQARQBDAFQASQBOAEYATwA+ADwASwBJAEQAPgAvAHIANgBIAEgASAB2AGkAbABFADIAWQBRAGkAKwBEAC8AKwBCAGIAQwBnAD0APQA8AC8ASwBJAEQAPgA8AEwAQQBfAFUAUgBMAD4AaAB0AHQAcABzADoALwAvAHAAbABhAHkAcgBlAGEAZAB5AC4AawBlAHkAbwBzAC4AYwBvAG0ALwBhAHAAaQAvAHYANAAvAGcAZQB0AEwAaQBjAGUAbgBzAGUAPAAvAEwAQQBfAFUAUgBMAD4APABEAFMAXwBJAEQAPgBWAGwAUgA3AEkAZABzAEkASgBFAHUAUgBkADAANgBMAGEAcQBzADIAagB3AD0APQA8AC8ARABTAF8ASQBEAD4APABDAFUAUwBUAE8ATQBBAFQAVABSAEkAQgBVAFQARQBTACAAeABtAGwAbgBzAD0AIgAiAD4APABDAEkARAA+AC8AcgA2AEgASABIAHYAaQBsAEUAMgBZAFEAaQArAEQALwArAEIAYgBDAGcAPQA9ADwALwBDAEkARAA+ADwARABSAE0AVABZAFAARQA+AHMAbQBvAG8AdABoADwALwBEAFIATQBUAFkAUABFAD4APAAvAEMAVQBTAFQATwBNAEEAVABUAFIASQBCAFUAVABFAFMAPgA8AEMASABFAEMASwBTAFUATQA+AGwAMgBOADAAMQBFAHgAVABaAFUAdwA9ADwALwBDAEgARQBDAEsAUwBVAE0APgA8AC8ARABBAFQAQQA+ADwALwBXAFIATQBIAEUAQQBEAEUAUgA+AAAAAFdwc3NoAAAAAO3vi6l51krOo8gn3NUdIe0AAAA3CAESEByHvv7ie02UmEIvg//gWwoaC2J1eWRybWtleW9zIhAch77+4ntNlJhCL4P/4FsKKgJIRA==</textarea>
            </div>
            
            <button class="button" onclick="decodePSSH()">🔍 فك تشفير PSSH</button>
            
            <div id="results" class="result-section" style="display: none;">
                <h3>📊 نتائج فك التشفير:</h3>
                <div id="resultContent"></div>
            </div>
        </div>
    </div>

    <script>
        // نسخ الدوال من ملف drm_inject.js
        const fromHexString_dld = hexString => Uint8Array.from(hexString.match(/.{1,2}/g).map(byte => parseInt(byte, 16)));
        
        const toHexString_dld = bytes => bytes.reduce((str, byte) => str + byte.toString(16).padStart(2, '0'), '');
        
        const b64ToHex_dld = b64 => [...atob(b64)].map(c=> c.charCodeAt(0).toString(16).padStart(2,0)).join``

        function decodePSSH() {
            const psshInput = document.getElementById('psshInput').value.trim();
            const resultsDiv = document.getElementById('results');
            const resultContent = document.getElementById('resultContent');
            
            if (!psshInput) {
                resultContent.innerHTML = '<div class="error">⚠️ يرجى إدخال PSSH صالح</div>';
                resultsDiv.style.display = 'block';
                return;
            }
            
            try {
                // فك تشفير Base64
                const binaryData = atob(psshInput);
                const uint8Array = new Uint8Array(binaryData.length);
                for (let i = 0; i < binaryData.length; i++) {
                    uint8Array[i] = binaryData.charCodeAt(i);
                }
                
                // تحويل إلى Hex
                const hexString = toHexString_dld(uint8Array);
                
                // استخراج معلومات PSSH
                const psshInfo = parsePSSH(uint8Array, hexString);
                
                let resultHTML = '<div class="success">✅ تم فك التشفير بنجاح!</div>';
                
                // عرض المعلومات الأساسية
                resultHTML += `
                    <div class="result-item">
                        <strong>📏 حجم البيانات:</strong> ${uint8Array.length} بايت
                    </div>
                    <div class="result-item">
                        <strong>🔢 البيانات الست عشرية:</strong>
                        <div class="hex-output">${hexString}</div>
                    </div>
                `;
                
                // عرض معلومات PSSH المستخرجة
                if (psshInfo.systemId) {
                    resultHTML += `
                        <div class="result-item">
                            <strong>🆔 معرف النظام:</strong> ${psshInfo.systemId}
                        </div>
                    `;
                }
                
                if (psshInfo.keyId) {
                    resultHTML += `
                        <div class="result-item">
                            <strong>🔑 معرف المفتاح:</strong> ${psshInfo.keyId}
                        </div>
                    `;
                }
                
                if (psshInfo.licenseUrl) {
                    resultHTML += `
                        <div class="result-item">
                            <strong>🌐 رابط الترخيص:</strong> <a href="${psshInfo.licenseUrl}" target="_blank">${psshInfo.licenseUrl}</a>
                        </div>
                    `;
                }
                
                if (psshInfo.contentId) {
                    resultHTML += `
                        <div class="result-item">
                            <strong>📄 معرف المحتوى:</strong> ${psshInfo.contentId}
                        </div>
                    `;
                }
                
                // عرض النص المستخرج
                if (psshInfo.extractedText) {
                    resultHTML += `
                        <div class="result-item">
                            <strong>📝 النص المستخرج:</strong>
                            <div class="hex-output">${psshInfo.extractedText}</div>
                        </div>
                    `;
                }
                
                resultContent.innerHTML = resultHTML;
                resultsDiv.style.display = 'block';
                
            } catch (error) {
                resultContent.innerHTML = `<div class="error">❌ خطأ في فك التشفير: ${error.message}</div>`;
                resultsDiv.style.display = 'block';
            }
        }
        
        function parsePSSH(uint8Array, hexString) {
            const result = {};
            
            try {
                // البحث عن PSSH header
                const psshIndex = hexString.indexOf('70737368');
                if (psshIndex !== -1) {
                    result.psshFound = true;
                }
                
                // استخراج System ID (16 bytes بعد PSSH header)
                if (psshIndex !== -1) {
                    const systemIdStart = (psshIndex + 16) / 2; // تحويل من hex إلى byte index
                    const systemIdBytes = uint8Array.slice(systemIdStart, systemIdStart + 16);
                    result.systemId = toHexString_dld(systemIdBytes);
                }
                
                // محاولة استخراج النص
                let extractedText = '';
                for (let i = 0; i < uint8Array.length; i++) {
                    const char = uint8Array[i];
                    if (char >= 32 && char <= 126) { // ASCII printable characters
                        extractedText += String.fromCharCode(char);
                    } else if (char === 0) {
                        extractedText += ' ';
                    }
                }
                result.extractedText = extractedText.replace(/\s+/g, ' ').trim();
                
                // البحث عن URL في النص
                const urlMatch = result.extractedText.match(/https?:\/\/[^\s<>]+/);
                if (urlMatch) {
                    result.licenseUrl = urlMatch[0];
                }
                
                // البحث عن Key ID patterns
                const keyIdMatch = hexString.match(/([0-9a-f]{32})/gi);
                if (keyIdMatch && keyIdMatch.length > 0) {
                    result.keyId = keyIdMatch[0];
                }
                
                // البحث عن Content ID
                const contentIdMatch = result.extractedText.match(/([A-Za-z0-9+/=]{20,})/);
                if (contentIdMatch) {
                    result.contentId = contentIdMatch[0];
                }
                
            } catch (error) {
                result.error = error.message;
            }
            
            return result;
        }
        
        // تشغيل فك التشفير تلقائياً عند تحميل الصفحة
        window.onload = function() {
            decodePSSH();
        };
    </script>
</body>
</html>
