<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Udemy Link Analyzer - محلل روابط Udemy</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            color: #333;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
            color: white;
            padding: 40px;
            text-align: center;
        }
        .content {
            padding: 40px;
        }
        .analysis-section {
            background: #f8f9fa;
            margin: 20px 0;
            padding: 25px;
            border-radius: 15px;
            border-left: 5px solid #3498db;
        }
        .code-block {
            background: #2d3748;
            color: #68d391;
            padding: 20px;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            word-break: break-all;
            margin: 15px 0;
            max-height: 400px;
            overflow-y: auto;
        }
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .info-card {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .info-card h4 {
            color: #2c3e50;
            margin-top: 0;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        .highlight {
            background: #fff3cd;
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; }
        .warning { background: #fff3cd; color: #856404; }
        .danger { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        
        .flow-diagram {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin: 20px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            flex-wrap: wrap;
        }
        .flow-step {
            background: #3498db;
            color: white;
            padding: 15px 20px;
            border-radius: 10px;
            margin: 5px;
            text-align: center;
            min-width: 120px;
            position: relative;
        }
        .flow-step::after {
            content: '→';
            position: absolute;
            right: -15px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 20px;
            color: #3498db;
        }
        .flow-step:last-child::after {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 Udemy Link Analyzer</h1>
            <p>تحليل متقدم لرابط فيديو Udemy المشفر</p>
        </div>
        
        <div class="content">
            <div class="analysis-section">
                <h3>🔗 تحليل الرابط الأساسي</h3>
                <div class="code-block" id="originalLink"></div>
                
                <div class="info-grid">
                    <div class="info-card">
                        <h4>📊 معلومات الأصل (Asset)</h4>
                        <div id="assetInfo"></div>
                    </div>
                    <div class="info-card">
                        <h4>🔐 معلومات التشفير</h4>
                        <div id="encryptionInfo"></div>
                    </div>
                    <div class="info-card">
                        <h4>🎫 معلومات Token</h4>
                        <div id="tokenInfo"></div>
                    </div>
                    <div class="info-card">
                        <h4>🌐 معلومات الشبكة</h4>
                        <div id="networkInfo"></div>
                    </div>
                </div>
            </div>

            <div class="analysis-section">
                <h3>⚡ تدفق عملية التحميل في الإضافة</h3>
                <div class="flow-diagram">
                    <div class="flow-step">استخراج MPD</div>
                    <div class="flow-step">تحليل Segments</div>
                    <div class="flow-step">استخراج PSSH</div>
                    <div class="flow-step">الحصول على المفاتيح</div>
                    <div class="flow-step">تحميل Segments</div>
                    <div class="flow-step">فك التشفير</div>
                    <div class="flow-step">دمج FFmpeg</div>
                </div>
            </div>

            <div class="analysis-section">
                <h3>🧩 تحليل مكونات الرابط</h3>
                <div id="urlComponents"></div>
            </div>

            <div class="analysis-section">
                <h3>🔧 كيف تعمل الإضافة مع هذا الرابط</h3>
                <div id="extensionProcess"></div>
            </div>

            <div class="analysis-section">
                <h3>💻 الكود المحاكي</h3>
                <div class="code-block" id="simulatedCode"></div>
            </div>
        </div>
    </div>

    <script>
        const originalUrl = 'https://www.udemy.com/assets/39324048/encrypted-files/out/v1/ab6818eaa27c464ab8afce6ecf1f499b/3e691b0da4414b83836363226ac161e9/ecd8798041b7474bb05daed9aae97b93/index.mpd?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************.3SEE4F5QZACFkw4xKNJOfiF23a3XlazMVuPG60X836g&provider=cloudfront&v=1&CMCD=ot%3Dm%2Csf%3Dd%2Csid%3D%222be19078-2228-4b1d-b6ce-50569e866453%22%2Csu';

        function analyzeUrl() {
            // عرض الرابط الأصلي
            document.getElementById('originalLink').textContent = originalUrl;

            try {
                const url = new URL(originalUrl);
                const pathParts = url.pathname.split('/').filter(part => part);

                // تحليل معلومات الأصل
                const assetId = pathParts[1]; // 39324048
                const hash1 = pathParts[5]; // ab6818eaa27c464ab8afce6ecf1f499b
                const hash2 = pathParts[6]; // 3e691b0da4414b83836363226ac161e9
                const hash3 = pathParts[7]; // ecd8798041b7474bb05daed9aae97b93

                document.getElementById('assetInfo').innerHTML = `
                    <p><strong>Asset ID:</strong> <span class="highlight success">${assetId}</span></p>
                    <p><strong>Hash Level 1:</strong> <span class="highlight info">${hash1}</span></p>
                    <p><strong>Hash Level 2:</strong> <span class="highlight info">${hash2}</span></p>
                    <p><strong>Hash Level 3:</strong> <span class="highlight info">${hash3}</span></p>
                    <p><strong>File Type:</strong> <span class="highlight warning">MPEG-DASH (.mpd)</span></p>
                `;

                // تحليل معلومات التشفير
                document.getElementById('encryptionInfo').innerHTML = `
                    <p><strong>نوع الحماية:</strong> <span class="highlight danger">DRM Protected</span></p>
                    <p><strong>مجلد التشفير:</strong> <span class="highlight warning">encrypted-files</span></p>
                    <p><strong>نظام التشفير:</strong> <span class="highlight info">CENC (Common Encryption)</span></p>
                    <p><strong>المفاتيح:</strong> <span class="highlight danger">مطلوبة من خادم DRM</span></p>
                `;

                // تحليل Token
                const token = url.searchParams.get('token');
                let tokenData = {};
                if (token) {
                    try {
                        const payload = JSON.parse(atob(token.split('.')[1]));
                        tokenData = payload;
                    } catch (e) {
                        tokenData = { error: 'فشل في فك تشفير Token' };
                    }
                }

                document.getElementById('tokenInfo').innerHTML = `
                    <p><strong>نوع Token:</strong> <span class="highlight info">JWT</span></p>
                    <p><strong>المسار:</strong> <span class="highlight">${tokenData.path || 'غير متاح'}</span></p>
                    <p><strong>انتهاء الصلاحية:</strong> <span class="highlight warning">${tokenData.exp ? new Date(tokenData.exp * 1000).toLocaleString('ar') : 'غير متاح'}</span></p>
                    <p><strong>الحالة:</strong> <span class="highlight ${Date.now() / 1000 < tokenData.exp ? 'success' : 'danger'}">${Date.now() / 1000 < tokenData.exp ? 'صالح' : 'منتهي الصلاحية'}</span></p>
                `;

                // تحليل معلومات الشبكة
                const cmcd = url.searchParams.get('CMCD');
                document.getElementById('networkInfo').innerHTML = `
                    <p><strong>المزود:</strong> <span class="highlight info">${url.searchParams.get('provider') || 'غير محدد'}</span></p>
                    <p><strong>الإصدار:</strong> <span class="highlight">${url.searchParams.get('v') || 'غير محدد'}</span></p>
                    <p><strong>CMCD:</strong> <span class="highlight warning">${cmcd ? 'متاح' : 'غير متاح'}</span></p>
                    <p><strong>الخادم:</strong> <span class="highlight info">CloudFront CDN</span></p>
                `;

                // تحليل مكونات URL
                document.getElementById('urlComponents').innerHTML = `
                    <div class="info-grid">
                        <div class="info-card">
                            <h4>🏗️ البنية الأساسية</h4>
                            <p><strong>Protocol:</strong> ${url.protocol}</p>
                            <p><strong>Host:</strong> ${url.hostname}</p>
                            <p><strong>Path:</strong> ${url.pathname}</p>
                        </div>
                        <div class="info-card">
                            <h4>📁 هيكل المجلدات</h4>
                            <p><strong>assets/</strong> - مجلد الأصول</p>
                            <p><strong>${assetId}/</strong> - معرف الأصل</p>
                            <p><strong>encrypted-files/</strong> - الملفات المشفرة</p>
                            <p><strong>out/v1/</strong> - مخرجات الإصدار 1</p>
                        </div>
                        <div class="info-card">
                            <h4>🔐 طبقات التشفير</h4>
                            <p><strong>Layer 1:</strong> ${hash1.substring(0, 8)}...</p>
                            <p><strong>Layer 2:</strong> ${hash2.substring(0, 8)}...</p>
                            <p><strong>Layer 3:</strong> ${hash3.substring(0, 8)}...</p>
                        </div>
                        <div class="info-card">
                            <h4>⚙️ معاملات الاستعلام</h4>
                            <p><strong>token:</strong> JWT Authentication</p>
                            <p><strong>provider:</strong> CloudFront</p>
                            <p><strong>v:</strong> Version 1</p>
                            <p><strong>CMCD:</strong> Common Media Client Data</p>
                        </div>
                    </div>
                `;

                // شرح عملية الإضافة
                document.getElementById('extensionProcess').innerHTML = `
                    <div class="info-grid">
                        <div class="info-card">
                            <h4>1️⃣ اكتشاف الفيديو</h4>
                            <p>• الإضافة تراقب طلبات الشبكة</p>
                            <p>• تكتشف ملفات .mpd تلقائياً</p>
                            <p>• تستخرج معلومات الفيديو من API</p>
                        </div>
                        <div class="info-card">
                            <h4>2️⃣ معالجة MPD</h4>
                            <p>• تحميل ملف index.mpd</p>
                            <p>• تحليل AdaptationSets</p>
                            <p>• استخراج قائمة Segments</p>
                        </div>
                        <div class="info-card">
                            <h4>3️⃣ استخراج PSSH</h4>
                            <p>• البحث عن ContentProtection</p>
                            <p>• استخراج PSSH من MPD</p>
                            <p>• تحديد نوع DRM (PlayReady/Widevine)</p>
                        </div>
                        <div class="info-card">
                            <h4>4️⃣ الحصول على المفاتيح</h4>
                            <p>• إرسال PSSH للخادم الخارجي</p>
                            <p>• معالجة Challenge/Response</p>
                            <p>• استلام مفاتيح فك التشفير</p>
                        </div>
                        <div class="info-card">
                            <h4>5️⃣ تحميل Segments</h4>
                            <p>• تحميل متوازي للـ segments</p>
                            <p>• فك تشفير كل segment</p>
                            <p>• تتبع التقدم والأخطاء</p>
                        </div>
                        <div class="info-card">
                            <h4>6️⃣ دمج الفيديو</h4>
                            <p>• استخدام FFmpeg WebAssembly</p>
                            <p>• دمج segments المفكوكة</p>
                            <p>• إنتاج ملف MP4 نهائي</p>
                        </div>
                    </div>
                `;

                // الكود المحاكي
                document.getElementById('simulatedCode').innerHTML = `
// كود محاكي لعملية الإضافة
class UdemyVideoProcessor {
    constructor(mpdUrl) {
        this.mpdUrl = mpdUrl;
        this.segments = [];
        this.pssh = '';
        this.keys = '';
    }

    async process() {
        // 1. جلب MPD
        const mpdContent = await this.fetchMPD();
        
        // 2. تحليل segments
        this.segments = this.parseSegments(mpdContent);
        
        // 3. استخراج PSSH
        this.pssh = this.extractPSSH(mpdContent);
        
        // 4. الحصول على المفاتيح
        this.keys = await this.getDRMKeys(this.pssh);
        
        // 5. تحميل وفك تشفير segments
        const decryptedSegments = await this.downloadAndDecrypt();
        
        // 6. دمج باستخدام FFmpeg
        const finalVideo = await this.mergeWithFFmpeg(decryptedSegments);
        
        return finalVideo;
    }

    async fetchMPD() {
        const response = await fetch(this.mpdUrl);
        return await response.text();
    }

    parseSegments(mpdContent) {
        // تحليل XML واستخراج segments
        const parser = new DOMParser();
        const doc = parser.parseFromString(mpdContent, 'text/xml');
        // ... logic to extract segments
        return segments;
    }

    extractPSSH(mpdContent) {
        // البحث عن ContentProtection واستخراج PSSH
        const psshMatch = mpdContent.match(/<pssh>([^<]+)</pssh>/);
        return psshMatch ? psshMatch[1] : '';
    }

    async getDRMKeys(pssh) {
        // إرسال PSSH للخادم والحصول على المفاتيح
        const response = await fetch('https://drm-server.com/getKeys', {
            method: 'POST',
            body: JSON.stringify({ pssh }),
            headers: { 'Content-Type': 'application/json' }
        });
        const data = await response.json();
        return data.keys;
    }

    async downloadAndDecrypt() {
        const decryptedSegments = [];
        
        for (const segment of this.segments) {
            // تحميل segment
            const encryptedData = await fetch(segment.url);
            
            // فك التشفير باستخدام WebAssembly
            const decryptedData = await this.decryptSegment(
                encryptedData, 
                this.keys
            );
            
            decryptedSegments.push(decryptedData);
        }
        
        return decryptedSegments;
    }

    async mergeWithFFmpeg(segments) {
        // استخدام FFmpeg WebAssembly لدمج segments
        const ffmpeg = new FFmpeg();
        await ffmpeg.load();
        
        // كتابة segments كملفات مؤقتة
        for (let i = 0; i < segments.length; i++) {
            await ffmpeg.writeFile(\`segment_\${i}.m4s\`, segments[i]);
        }
        
        // دمج الملفات
        await ffmpeg.exec([
            '-i', 'concat:segment_0.m4s|segment_1.m4s|...',
            '-c', 'copy',
            'output.mp4'
        ]);
        
        // قراءة الملف النهائي
        const finalVideo = await ffmpeg.readFile('output.mp4');
        return finalVideo;
    }
}

// استخدام المعالج
const processor = new UdemyVideoProcessor('${originalUrl}');
processor.process().then(video => {
    console.log('تم تحميل الفيديو بنجاح!', video);
});
                `;

            } catch (error) {
                console.error('خطأ في تحليل الرابط:', error);
            }
        }

        // تشغيل التحليل عند تحميل الصفحة
        window.onload = analyzeUrl;
    </script>
</body>
</html>
