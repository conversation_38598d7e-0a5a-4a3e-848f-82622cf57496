# 🎬 Udemy Video Downloader - Python Implementation

محمل فيديوهات Udemy حقيقي مكتوب بـ Python يعمل مع الروابط المشفرة.

## ✨ المميزات

- ✅ **تحليل روابط MPD** - استخراج معلومات الفيديو من MPEG-DASH
- ✅ **تحليل DRM** - فهم أنظمة التشفير المستخدمة
- ✅ **تحميل Segments** - تحميل متوازي لأجزاء الفيديو
- ✅ **دمج FFmpeg** - دمج الأجزاء لإنتاج فيديو كامل
- ✅ **تقارير مفصلة** - تحليل شامل للعملية

## 📋 المتطلبات

### البرامج المطلوبة:
- **Python 3.7+** - [تحميل Python](https://python.org)
- **FFmpeg** - [تحميل FFmpeg](https://ffmpeg.org)

### المكتبات المطلوبة:
```bash
pip install -r requirements.txt
```

## 🚀 التثبيت والتشغيل

### الطريقة السريعة:
```bash
python setup_and_run.py
```

### الطريقة اليدوية:

1. **تثبيت المكتبات:**
```bash
pip install requests lxml beautifulsoup4 pycryptodome tqdm
```

2. **تثبيت FFmpeg:**

**Windows:**
```bash
# باستخدام Chocolatey
choco install ffmpeg

# أو تحميل يدوي من https://ffmpeg.org
```

**macOS:**
```bash
brew install ffmpeg
```

**Linux:**
```bash
# Ubuntu/Debian
sudo apt install ffmpeg

# CentOS/RHEL
sudo yum install ffmpeg
```

3. **تشغيل المحمل:**
```bash
python udemy_video_downloader.py
```

## 📁 هيكل الملفات

```
📦 udemy-video-downloader/
├── 📄 udemy_video_downloader.py    # المحمل الرئيسي
├── 📄 drm_analyzer.py              # محلل DRM
├── 📄 setup_and_run.py             # سكريبت التثبيت
├── 📄 requirements.txt             # المكتبات المطلوبة
├── 📄 README.md                    # هذا الملف
├── 📁 downloads/                   # مجلد التحميلات
│   ├── 📄 manifest_39324048.mpd    # ملف MPD
│   ├── 📄 pssh_39324048.json       # معلومات PSSH
│   ├── 📁 segments_39324048/       # أجزاء الفيديو
│   └── 📄 video_39324048.mp4       # الفيديو النهائي
└── 📄 drm_analysis_report.json     # تقرير تحليل DRM
```

## 🔧 الاستخدام

### 1. تحميل فيديو:
```python
from udemy_video_downloader import UdemyVideoDownloader

mpd_url = "https://www.udemy.com/assets/.../index.mpd?token=..."
downloader = UdemyVideoDownloader()
success = downloader.process_video(mpd_url)
```

### 2. تحليل DRM:
```python
from drm_analyzer import DRMAnalyzer

analyzer = DRMAnalyzer()
report = analyzer.generate_report(mpd_url)
```

## 📊 مثال على الإخراج

```
🎬 بدء معالجة فيديو Udemy...
📁 مجلد الإخراج: downloads
🔍 تحليل رابط MPD...
✅ Asset ID: 39324048
✅ Token expires: Mon Jul 14 12:45:52 2025
📥 جلب ملف MPD...
✅ تم جلب MPD بنجاح (2847 bytes)
💾 تم حفظ MPD: downloads/manifest_39324048.mpd
🧩 تحليل محتوى MPD...
📹 وجد AdaptationSet: video/mp4 - avc1.640028
🔐 وجد PSSH: urn:uuid:9a04f079-9840-4286-ab92-e65be0885f95
✅ وجد 10 segments
✅ وجد 1 PSSH entries
🔐 تم حفظ PSSH: downloads/pssh_39324048.json
⬇️ بدء تحميل 10 segments...
✅ تم تحميل segment 1 (156789 bytes)
✅ تم تحميل segment 2 (234567 bytes)
...
📊 تم تحميل 10 segments بنجاح
🔧 دمج segments باستخدام FFmpeg...
✅ تم دمج الفيديو بنجاح: downloads/video_39324048.mp4
🎉 تم تحميل الفيديو بنجاح!
📁 الملف: downloads/video_39324048.mp4
📊 حجم الملف: 45.67 MB
```

## ⚠️ ملاحظات مهمة

### القيود:
- **DRM Protection**: الفيديوهات المحمية تحتاج مفاتيح فك تشفير
- **Token Expiry**: الروابط لها مدة صلاحية محدودة
- **Rate Limiting**: قد يحدث تحديد للسرعة من الخادم

### الاستخدام القانوني:
- ✅ **للاستخدام الشخصي** فقط
- ✅ **مع اشتراك صالح** في Udemy
- ❌ **لا تشارك** الملفات المحملة
- ❌ **لا تستخدم تجارياً**

## 🔐 تحليل DRM

السكريبت يدعم تحليل أنظمة DRM التالية:
- **PlayReady** (Microsoft)
- **Widevine** (Google)
- **FairPlay** (Apple)
- **ClearKey** (W3C)

## 🐛 استكشاف الأخطاء

### خطأ في جلب MPD:
```
❌ خطأ في جلب MPD: 403 Forbidden
```
**الحل**: تحقق من صلاحية Token أو الاشتراك

### خطأ FFmpeg:
```
❌ FFmpeg غير مثبت
```
**الحل**: ثبت FFmpeg حسب نظام التشغيل

### خطأ في التحميل:
```
❌ فشل تحميل segment: Connection timeout
```
**الحل**: تحقق من الاتصال بالإنترنت أو أعد المحاولة

## 📞 الدعم

إذا واجهت مشاكل:
1. تحقق من المتطلبات
2. راجع ملف التقرير المُنشأ
3. تأكد من صلاحية الرابط
4. تحقق من إعدادات الشبكة

## 📄 الترخيص

هذا المشروع للأغراض التعليمية والاستخدام الشخصي فقط.
يرجى احترام حقوق الطبع والنشر وشروط خدمة Udemy.

---

**تم تطويره بـ ❤️ للمجتمع العربي**
