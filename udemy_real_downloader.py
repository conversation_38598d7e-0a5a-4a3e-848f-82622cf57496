#!/usr/bin/env python3
"""
Real Udemy Video Downloader with FFmpeg
محمل فيديوهات Udemy الحقيقي مع FFmpeg
"""

import requests
import json
import base64
import xml.etree.ElementTree as ET
from urllib.parse import urljoin, urlparse
import os
import sys
import time
from pathlib import Path
import tempfile
import subprocess
from concurrent.futures import ThreadPoolExecutor, as_completed
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
import re
from tqdm import tqdm

class RealUdemyDownloader:
    def __init__(self):
        self.session = self.create_session()
        self.temp_dir = tempfile.mkdtemp(prefix="udemy_")
        self.ffmpeg_path = self.find_ffmpeg()
        
    def create_session(self):
        """إنشاء session محسن"""
        session = requests.Session()
        
        session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'video/mp4,video/*,*/*;q=0.9',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Cache-Control': 'no-cache',
            'Sec-Fetch-Dest': 'video',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'cross-site',
            'Referer': 'https://www.udemy.com/',
        })
        
        retry_strategy = Retry(
            total=5,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
        )
        
        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        
        return session
    
    def find_ffmpeg(self):
        """البحث عن FFmpeg"""
        # البحث في المجلد الحالي أولاً
        local_ffmpeg = os.path.join(os.getcwd(), 'ffmpeg.exe')
        if os.path.exists(local_ffmpeg):
            print(f"✅ وجد FFmpeg محلي: {local_ffmpeg}")
            return local_ffmpeg
        
        # البحث في PATH
        try:
            result = subprocess.run(['ffmpeg', '-version'], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                print("✅ وجد FFmpeg في PATH")
                return 'ffmpeg'
        except FileNotFoundError:
            pass
        
        print("⚠️ FFmpeg غير موجود")
        return None
    
    def download_ffmpeg(self):
        """تحميل FFmpeg تلقائياً"""
        print("📥 تحميل FFmpeg...")
        
        ffmpeg_url = "https://github.com/BtbN/FFmpeg-Builds/releases/download/latest/ffmpeg-master-latest-win64-gpl.zip"
        
        try:
            response = self.session.get(ffmpeg_url, stream=True)
            response.raise_for_status()
            
            zip_file = "ffmpeg.zip"
            with open(zip_file, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            # استخراج FFmpeg
            import zipfile
            with zipfile.ZipFile(zip_file, 'r') as zip_ref:
                # البحث عن ffmpeg.exe
                for file_info in zip_ref.filelist:
                    if file_info.filename.endswith('ffmpeg.exe'):
                        # استخراج ffmpeg.exe فقط
                        with zip_ref.open(file_info) as source:
                            with open('ffmpeg.exe', 'wb') as target:
                                target.write(source.read())
                        break
            
            os.remove(zip_file)
            
            if os.path.exists('ffmpeg.exe'):
                print("✅ تم تحميل FFmpeg بنجاح")
                return os.path.abspath('ffmpeg.exe')
            
        except Exception as e:
            print(f"❌ فشل تحميل FFmpeg: {e}")
        
        return None
    
    def analyze_url(self, mpd_url):
        """تحليل URL"""
        print("🔍 تحليل رابط الفيديو...")
        
        parsed = urlparse(mpd_url)
        path_parts = parsed.path.split('/')
        
        info = {
            'asset_id': path_parts[2] if len(path_parts) > 2 else None,
            'base_url': f"{parsed.scheme}://{parsed.netloc}{'/'.join(path_parts[:-1])}",
            'query_params': dict(param.split('=') for param in parsed.query.split('&') if '=' in param)
        }
        
        # تحليل Token
        token = info['query_params'].get('token')
        if token:
            try:
                payload = token.split('.')[1]
                payload += '=' * (4 - len(payload) % 4)
                decoded = json.loads(base64.b64decode(payload))
                info['token'] = {
                    'exp': decoded.get('exp'),
                    'is_valid': time.time() < decoded.get('exp', 0)
                }
            except:
                pass
        
        print(f"✅ Asset ID: {info['asset_id']}")
        return info
    
    def fetch_mpd(self, mpd_url):
        """جلب ملف MPD"""
        print("📥 جلب ملف MPD...")
        
        try:
            response = self.session.get(mpd_url, timeout=30)
            response.raise_for_status()
            print(f"✅ تم جلب MPD ({len(response.content)} bytes)")
            return response.text
        except Exception as e:
            print(f"❌ خطأ في جلب MPD: {e}")
            return None
    
    def parse_mpd(self, mpd_content, base_url):
        """تحليل MPD واستخراج segments"""
        print("🧩 تحليل MPD...")
        
        try:
            root = ET.fromstring(mpd_content)
            namespaces = {'mpd': 'urn:mpeg:dash:schema:mpd:2011'}
            
            segments = []
            best_video = None
            best_audio = None
            
            # البحث عن أفضل جودة فيديو وصوت
            for adaptation_set in root.findall('.//mpd:AdaptationSet', namespaces):
                mime_type = adaptation_set.get('mimeType', '')
                
                for representation in adaptation_set.findall('.//mpd:Representation', namespaces):
                    bandwidth = int(representation.get('bandwidth', '0'))
                    
                    if 'video' in mime_type:
                        if best_video is None or bandwidth > best_video['bandwidth']:
                            best_video = {
                                'bandwidth': bandwidth,
                                'width': representation.get('width'),
                                'height': representation.get('height'),
                                'representation': representation
                            }
                    elif 'audio' in mime_type:
                        if best_audio is None or bandwidth > best_audio['bandwidth']:
                            best_audio = {
                                'bandwidth': bandwidth,
                                'representation': representation
                            }
            
            # استخراج segments من أفضل جودة
            for media_type, best_rep in [('video', best_video), ('audio', best_audio)]:
                if best_rep is None:
                    continue
                
                representation = best_rep['representation']
                segment_template = representation.find('.//mpd:SegmentTemplate', namespaces)
                
                if segment_template is not None:
                    media_template = segment_template.get('media')
                    init_template = segment_template.get('initialization')
                    
                    if media_template:
                        # استخراج عدد segments من SegmentTimeline
                        timeline = segment_template.find('.//mpd:SegmentTimeline', namespaces)
                        if timeline is not None:
                            segment_count = 0
                            for s_element in timeline.findall('.//mpd:S', namespaces):
                                r = int(s_element.get('r', '0'))
                                segment_count += r + 1
                            
                            print(f"📹 {media_type}: {segment_count} segments")
                            
                            # إنشاء URLs للـ segments
                            for i in range(1, min(segment_count + 1, 21)):  # أول 20 segment
                                segment_url = media_template.replace('$Number$', str(i))
                                
                                # إضافة base URL إذا لم يكن absolute
                                if not segment_url.startswith('http'):
                                    segment_url = urljoin(base_url + '/', segment_url)
                                
                                segments.append({
                                    'number': i,
                                    'url': segment_url,
                                    'type': media_type,
                                    'filename': f"{media_type}_{i:04d}.mp4"
                                })
                            
                            # إضافة initialization segment
                            if init_template:
                                init_url = init_template
                                if not init_url.startswith('http'):
                                    init_url = urljoin(base_url + '/', init_url)
                                
                                segments.insert(0, {
                                    'number': 0,
                                    'url': init_url,
                                    'type': media_type,
                                    'filename': f"{media_type}_init.mp4"
                                })
            
            print(f"✅ تم استخراج {len(segments)} segments")
            return segments
            
        except Exception as e:
            print(f"❌ خطأ في تحليل MPD: {e}")
            return []
    
    def download_segment(self, segment, output_dir):
        """تحميل segment واحد"""
        try:
            response = self.session.get(segment['url'], timeout=30)
            response.raise_for_status()
            
            filepath = os.path.join(output_dir, segment['filename'])
            with open(filepath, 'wb') as f:
                f.write(response.content)
            
            return {
                'success': True,
                'segment': segment['number'],
                'type': segment['type'],
                'size': len(response.content),
                'file': filepath
            }
            
        except Exception as e:
            return {
                'success': False,
                'segment': segment['number'],
                'type': segment['type'],
                'error': str(e)
            }
    
    def download_segments(self, segments, output_dir, max_workers=3):
        """تحميل جميع segments"""
        print(f"⬇️ تحميل {len(segments)} segments...")
        
        os.makedirs(output_dir, exist_ok=True)
        
        downloaded = []
        failed = []
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # إرسال المهام
            future_to_segment = {
                executor.submit(self.download_segment, segment, output_dir): segment 
                for segment in segments
            }
            
            # معالجة النتائج مع progress bar
            with tqdm(total=len(segments), desc="تحميل", unit="segment") as pbar:
                for future in as_completed(future_to_segment):
                    result = future.result()
                    
                    if result['success']:
                        downloaded.append(result)
                        pbar.set_postfix({
                            'نجح': len(downloaded),
                            'فشل': len(failed),
                            'النوع': result['type']
                        })
                    else:
                        failed.append(result)
                        print(f"❌ فشل {result['type']} segment {result['segment']}: {result['error']}")
                    
                    pbar.update(1)
        
        print(f"✅ تم تحميل {len(downloaded)} segments")
        print(f"❌ فشل {len(failed)} segments")
        
        return downloaded, failed
    
    def merge_with_ffmpeg(self, downloaded_segments, output_file):
        """دمج segments باستخدام FFmpeg"""
        if not self.ffmpeg_path:
            print("❌ FFmpeg غير متاح")
            return False
        
        print("🔧 دمج segments باستخدام FFmpeg...")
        
        # تجميع segments حسب النوع
        video_segments = [s for s in downloaded_segments if s['type'] == 'video']
        audio_segments = [s for s in downloaded_segments if s['type'] == 'audio']
        
        if not video_segments:
            print("❌ لا توجد video segments")
            return False
        
        try:
            # ترتيب segments
            video_segments.sort(key=lambda x: x['segment'])
            audio_segments.sort(key=lambda x: x['segment'])
            
            # إنشاء ملف concat للفيديو
            video_concat = os.path.join(self.temp_dir, 'video_list.txt')
            with open(video_concat, 'w') as f:
                for segment in video_segments:
                    f.write(f"file '{segment['file']}'\n")
            
            # دمج الفيديو أولاً
            video_output = os.path.join(self.temp_dir, 'video_merged.mp4')
            cmd = [
                self.ffmpeg_path,
                '-f', 'concat',
                '-safe', '0',
                '-i', video_concat,
                '-c', 'copy',
                '-y',
                video_output
            ]
            
            print("🔧 دمج الفيديو...")
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode != 0:
                print(f"❌ خطأ في دمج الفيديو: {result.stderr}")
                return False
            
            # إذا كان هناك صوت، دمجه مع الفيديو
            if audio_segments:
                audio_concat = os.path.join(self.temp_dir, 'audio_list.txt')
                with open(audio_concat, 'w') as f:
                    for segment in audio_segments:
                        f.write(f"file '{segment['file']}'\n")
                
                audio_output = os.path.join(self.temp_dir, 'audio_merged.mp4')
                cmd = [
                    self.ffmpeg_path,
                    '-f', 'concat',
                    '-safe', '0',
                    '-i', audio_concat,
                    '-c', 'copy',
                    '-y',
                    audio_output
                ]
                
                print("🔧 دمج الصوت...")
                result = subprocess.run(cmd, capture_output=True, text=True)
                
                if result.returncode == 0:
                    # دمج الفيديو والصوت معاً
                    cmd = [
                        self.ffmpeg_path,
                        '-i', video_output,
                        '-i', audio_output,
                        '-c', 'copy',
                        '-y',
                        output_file
                    ]
                    
                    print("🔧 دمج الفيديو والصوت...")
                    result = subprocess.run(cmd, capture_output=True, text=True)
                else:
                    # استخدام الفيديو فقط
                    import shutil
                    shutil.copy2(video_output, output_file)
            else:
                # استخدام الفيديو فقط
                import shutil
                shutil.copy2(video_output, output_file)
            
            if os.path.exists(output_file):
                size_mb = os.path.getsize(output_file) / (1024 * 1024)
                print(f"✅ تم إنشاء الفيديو: {output_file}")
                print(f"📊 حجم الملف: {size_mb:.2f} MB")
                return True
            else:
                print("❌ فشل في إنشاء الملف النهائي")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في FFmpeg: {e}")
            return False
    
    def download_video(self, mpd_url, output_dir="downloads"):
        """تحميل الفيديو الكامل"""
        print("🎬 بدء تحميل فيديو Udemy...")
        
        # التحقق من FFmpeg
        if not self.ffmpeg_path:
            print("📥 محاولة تحميل FFmpeg...")
            self.ffmpeg_path = self.download_ffmpeg()
            if not self.ffmpeg_path:
                print("❌ لا يمكن المتابعة بدون FFmpeg")
                return False
        
        # تحليل URL
        url_info = self.analyze_url(mpd_url)
        if not url_info['asset_id']:
            return False
        
        asset_id = url_info['asset_id']
        os.makedirs(output_dir, exist_ok=True)
        
        # جلب MPD
        mpd_content = self.fetch_mpd(mpd_url)
        if not mpd_content:
            return False
        
        # تحليل segments
        segments = self.parse_mpd(mpd_content, url_info['base_url'])
        if not segments:
            return False
        
        # تحميل segments
        segments_dir = os.path.join(output_dir, f"segments_{asset_id}")
        downloaded, failed = self.download_segments(segments, segments_dir)
        
        if not downloaded:
            print("❌ لم يتم تحميل أي segments")
            return False
        
        # دمج الفيديو
        output_file = os.path.join(output_dir, f"udemy_video_{asset_id}.mp4")
        success = self.merge_with_ffmpeg(downloaded, output_file)
        
        if success:
            print(f"🎉 تم تحميل الفيديو بنجاح!")
            print(f"📁 الملف: {output_file}")
            
            # تنظيف الملفات المؤقتة
            try:
                import shutil
                shutil.rmtree(segments_dir)
                print("🧹 تم تنظيف الملفات المؤقتة")
            except:
                pass
        
        return success

def main():
    """الدالة الرئيسية"""
    mpd_url = "https://www.udemy.com/assets/39324048/encrypted-files/out/v1/ab6818eaa27c464ab8afce6ecf1f499b/3e691b0da4414b83836363226ac161e9/ecd8798041b7474bb05daed9aae97b93/index.mpd?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************.3SEE4F5QZACFkw4xKNJOfiF23a3XlazMVuPG60X836g&provider=cloudfront&v=1&CMCD=ot%3Dm%2Csf%3Dd%2Csid%3D%222be19078-2228-4b1d-b6ce-50569e866453%22%2Csu"
    
    downloader = RealUdemyDownloader()
    success = downloader.download_video(mpd_url)
    
    if success:
        print("\n✅ تم تحميل الفيديو بنجاح!")
    else:
        print("\n❌ فشل في تحميل الفيديو")
        sys.exit(1)

if __name__ == "__main__":
    main()
