#!/usr/bin/env python3
"""
Udemy Video Downloader - Real Implementation
تحميل فيديوهات Udemy الحقيقي باستخدام Python
"""

import requests
import json
import base64
import xml.etree.ElementTree as ET
from urllib.parse import urljoin, urlparse
import os
import sys
from pathlib import Path
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
import subprocess
import tempfile

class UdemyVideoDownloader:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': '*/*',
            'Accept-Language': 'en-US,en;q=0.9,ar;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
            'Referer': 'https://www.udemy.com/',
            'Origin': 'https://www.udemy.com'
        })

        # إضافة retry strategy
        from requests.adapters import HTTPAdapter
        from urllib3.util.retry import Retry

        retry_strategy = Retry(
            total=3,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
        )

        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)
        
    def analyze_mpd_url(self, mpd_url):
        """تحليل رابط MPD واستخراج المعلومات"""
        print("🔍 تحليل رابط MPD...")
        
        parsed_url = urlparse(mpd_url)
        path_parts = parsed_url.path.split('/')
        
        info = {
            'asset_id': path_parts[2] if len(path_parts) > 2 else None,
            'hash1': path_parts[6] if len(path_parts) > 6 else None,
            'hash2': path_parts[7] if len(path_parts) > 7 else None,
            'hash3': path_parts[8] if len(path_parts) > 8 else None,
            'base_url': f"{parsed_url.scheme}://{parsed_url.netloc}{'/'.join(path_parts[:-1])}",
            'full_url': mpd_url
        }
        
        # تحليل JWT Token
        if 'token=' in mpd_url:
            token = mpd_url.split('token=')[1].split('&')[0]
            try:
                # فك تشفير JWT payload
                payload = token.split('.')[1]
                # إضافة padding إذا لزم الأمر
                payload += '=' * (4 - len(payload) % 4)
                decoded = json.loads(base64.b64decode(payload))
                info['token_info'] = decoded
                info['token_expires'] = time.ctime(decoded.get('exp', 0))
            except Exception as e:
                info['token_error'] = str(e)
        
        print(f"✅ Asset ID: {info['asset_id']}")
        print(f"✅ Token expires: {info.get('token_expires', 'Unknown')}")
        
        return info
    
    def fetch_mpd_content(self, mpd_url):
        """جلب محتوى ملف MPD"""
        print("📥 جلب ملف MPD...")
        
        try:
            response = self.session.get(mpd_url, timeout=30)
            response.raise_for_status()
            
            print(f"✅ تم جلب MPD بنجاح ({len(response.content)} bytes)")
            return response.text
            
        except requests.exceptions.RequestException as e:
            print(f"❌ خطأ في جلب MPD: {e}")
            return None
    
    def parse_mpd_content(self, mpd_content, base_url):
        """تحليل محتوى MPD واستخراج المعلومات"""
        print("🧩 تحليل محتوى MPD...")
        
        try:
            root = ET.fromstring(mpd_content)
            
            # البحث عن namespaces
            namespaces = {
                'mpd': 'urn:mpeg:dash:schema:mpd:2011',
                'cenc': 'urn:mpeg:cenc:2013'
            }
            
            segments = []
            pssh_data = []
            
            # البحث عن AdaptationSets
            for adaptation_set in root.findall('.//mpd:AdaptationSet', namespaces):
                mime_type = adaptation_set.get('mimeType', '')
                codecs = adaptation_set.get('codecs', '')
                
                print(f"📹 وجد AdaptationSet: {mime_type} - {codecs}")
                
                # البحث عن ContentProtection و PSSH
                for content_protection in adaptation_set.findall('.//mpd:ContentProtection', namespaces):
                    scheme_id = content_protection.get('schemeIdUri', '')
                    
                    # البحث عن PSSH
                    pssh_element = content_protection.find('pssh')
                    if pssh_element is not None and pssh_element.text:
                        pssh_data.append({
                            'scheme': scheme_id,
                            'pssh': pssh_element.text.strip()
                        })
                        print(f"🔐 وجد PSSH: {scheme_id}")
                
                # البحث عن Representations
                for representation in adaptation_set.findall('.//mpd:Representation', namespaces):
                    bandwidth = representation.get('bandwidth')
                    width = representation.get('width')
                    height = representation.get('height')
                    
                    # البحث عن SegmentTemplate
                    segment_template = representation.find('.//mpd:SegmentTemplate', namespaces)
                    if segment_template is not None:
                        media_template = segment_template.get('media')
                        init_template = segment_template.get('initialization')
                        start_number = int(segment_template.get('startNumber', '1'))
                        
                        if media_template:
                            # إنشاء قائمة segments (محدودة للاختبار)
                            for i in range(start_number, start_number + 10):  # أول 10 segments فقط
                                segment_url = media_template.replace('$Number$', str(i))
                                full_segment_url = urljoin(base_url + '/', segment_url)
                                
                                segments.append({
                                    'number': i,
                                    'url': full_segment_url,
                                    'type': mime_type,
                                    'bandwidth': bandwidth,
                                    'resolution': f"{width}x{height}" if width and height else None
                                })
            
            print(f"✅ وجد {len(segments)} segments")
            print(f"✅ وجد {len(pssh_data)} PSSH entries")
            
            return {
                'segments': segments,
                'pssh_data': pssh_data,
                'duration': self.extract_duration(root, namespaces)
            }
            
        except ET.ParseError as e:
            print(f"❌ خطأ في تحليل XML: {e}")
            return None
        except Exception as e:
            print(f"❌ خطأ عام في التحليل: {e}")
            return None
    
    def extract_duration(self, root, namespaces):
        """استخراج مدة الفيديو"""
        try:
            duration_attr = root.get('mediaPresentationDuration')
            if duration_attr:
                # تحويل ISO 8601 duration إلى ثواني
                # مثال: PT1H30M45S
                import re
                match = re.match(r'PT(?:(\d+)H)?(?:(\d+)M)?(?:(\d+(?:\.\d+)?)S)?', duration_attr)
                if match:
                    hours = int(match.group(1) or 0)
                    minutes = int(match.group(2) or 0)
                    seconds = float(match.group(3) or 0)
                    total_seconds = hours * 3600 + minutes * 60 + seconds
                    return total_seconds
        except:
            pass
        return None
    
    def download_segment(self, segment_info, output_dir):
        """تحميل segment واحد"""
        try:
            # إنشاء المجلد إذا لم يكن موجوداً
            os.makedirs(output_dir, exist_ok=True)

            response = self.session.get(segment_info['url'], timeout=30)
            response.raise_for_status()

            filename = f"segment_{segment_info['number']:04d}.m4s"
            filepath = os.path.join(output_dir, filename)

            with open(filepath, 'wb') as f:
                f.write(response.content)

            return {
                'success': True,
                'segment': segment_info['number'],
                'size': len(response.content),
                'file': filepath
            }

        except Exception as e:
            return {
                'success': False,
                'segment': segment_info['number'],
                'error': str(e)
            }
    
    def download_segments(self, segments, output_dir, max_workers=5):
        """تحميل جميع segments بشكل متوازي"""
        print(f"⬇️ بدء تحميل {len(segments)} segments...")
        
        os.makedirs(output_dir, exist_ok=True)
        
        downloaded = []
        failed = []
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # إرسال جميع المهام
            future_to_segment = {
                executor.submit(self.download_segment, segment, output_dir): segment 
                for segment in segments
            }
            
            # معالجة النتائج
            for future in as_completed(future_to_segment):
                result = future.result()
                
                if result['success']:
                    downloaded.append(result)
                    print(f"✅ تم تحميل segment {result['segment']} ({result['size']} bytes)")
                else:
                    failed.append(result)
                    print(f"❌ فشل تحميل segment {result['segment']}: {result['error']}")
        
        print(f"📊 تم تحميل {len(downloaded)} segments بنجاح")
        print(f"📊 فشل تحميل {len(failed)} segments")
        
        return downloaded, failed
    
    def merge_segments_with_ffmpeg(self, downloaded_segments, output_file):
        """دمج segments باستخدام FFmpeg"""
        print("🔧 دمج segments باستخدام FFmpeg...")
        
        try:
            # التحقق من وجود FFmpeg
            subprocess.run(['ffmpeg', '-version'], 
                         capture_output=True, check=True)
        except (subprocess.CalledProcessError, FileNotFoundError):
            print("❌ FFmpeg غير مثبت. يرجى تثبيت FFmpeg أولاً")
            return False
        
        # إنشاء ملف concat list
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            concat_file = f.name
            for segment in sorted(downloaded_segments, key=lambda x: x['segment']):
                f.write(f"file '{segment['file']}'\n")
        
        try:
            # تشغيل FFmpeg
            cmd = [
                'ffmpeg',
                '-f', 'concat',
                '-safe', '0',
                '-i', concat_file,
                '-c', 'copy',
                '-y',  # overwrite output file
                output_file
            ]
            
            print(f"🔧 تشغيل: {' '.join(cmd)}")
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                print(f"✅ تم دمج الفيديو بنجاح: {output_file}")
                return True
            else:
                print(f"❌ خطأ في FFmpeg: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في تشغيل FFmpeg: {e}")
            return False
        finally:
            # حذف ملف concat المؤقت
            try:
                os.unlink(concat_file)
            except:
                pass
    
    def process_video(self, mpd_url, output_dir="downloads"):
        """معالجة الفيديو الكاملة"""
        print("🎬 بدء معالجة فيديو Udemy...")
        print(f"📁 مجلد الإخراج: {output_dir}")
        
        # 1. تحليل URL
        url_info = self.analyze_mpd_url(mpd_url)
        if not url_info['asset_id']:
            print("❌ فشل في تحليل رابط MPD")
            return False
        
        # 2. جلب MPD
        mpd_content = self.fetch_mpd_content(mpd_url)
        if not mpd_content:
            return False
        
        # حفظ MPD للمراجعة
        mpd_file = os.path.join(output_dir, f"manifest_{url_info['asset_id']}.mpd")
        os.makedirs(output_dir, exist_ok=True)
        with open(mpd_file, 'w', encoding='utf-8') as f:
            f.write(mpd_content)
        print(f"💾 تم حفظ MPD: {mpd_file}")
        
        # 3. تحليل MPD
        parsed_data = self.parse_mpd_content(mpd_content, url_info['base_url'])
        if not parsed_data:
            return False
        
        # حفظ معلومات PSSH
        if parsed_data['pssh_data']:
            pssh_file = os.path.join(output_dir, f"pssh_{url_info['asset_id']}.json")
            with open(pssh_file, 'w', encoding='utf-8') as f:
                json.dump(parsed_data['pssh_data'], f, indent=2)
            print(f"🔐 تم حفظ PSSH: {pssh_file}")
        
        # 4. تحميل segments
        segments_dir = os.path.join(output_dir, f"segments_{url_info['asset_id']}")
        downloaded, failed = self.download_segments(
            parsed_data['segments'], 
            segments_dir
        )
        
        if not downloaded:
            print("❌ لم يتم تحميل أي segments")
            return False
        
        # 5. دمج segments
        output_video = os.path.join(output_dir, f"video_{url_info['asset_id']}.mp4")
        success = self.merge_segments_with_ffmpeg(downloaded, output_video)
        
        if success:
            print(f"🎉 تم تحميل الفيديو بنجاح!")
            print(f"📁 الملف: {output_video}")
            
            # إحصائيات
            if os.path.exists(output_video):
                size_mb = os.path.getsize(output_video) / (1024 * 1024)
                print(f"📊 حجم الملف: {size_mb:.2f} MB")
        
        return success

def main():
    """الدالة الرئيسية"""
    # رابط الفيديو الذي أرسلته
    mpd_url = "https://www.udemy.com/assets/39324048/encrypted-files/out/v1/ab6818eaa27c464ab8afce6ecf1f499b/3e691b0da4414b83836363226ac161e9/ecd8798041b7474bb05daed9aae97b93/index.mpd?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************.3SEE4F5QZACFkw4xKNJOfiF23a3XlazMVuPG60X836g&provider=cloudfront&v=1&CMCD=ot%3Dm%2Csf%3Dd%2Csid%3D%222be19078-2228-4b1d-b6ce-50569e866453%22%2Csu"
    
    # إنشاء المحمل
    downloader = UdemyVideoDownloader()
    
    # معالجة الفيديو
    success = downloader.process_video(mpd_url)
    
    if success:
        print("\n🎉 تم إكمال العملية بنجاح!")
    else:
        print("\n❌ فشلت العملية")
        sys.exit(1)

if __name__ == "__main__":
    main()
