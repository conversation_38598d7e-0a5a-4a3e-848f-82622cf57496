<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Udemy Video Processor - معالج فيديو Udemy</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            color: #333;
        }
        .container {
            max-width: 1600px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 40px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .content {
            padding: 40px;
        }
        .step {
            background: #f8f9fa;
            margin: 20px 0;
            padding: 25px;
            border-radius: 15px;
            border-left: 5px solid #667eea;
        }
        .step.active {
            border-left-color: #28a745;
            background: #d4edda;
        }
        .step.error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        .step h3 {
            color: #495057;
            margin-top: 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .input-group {
            margin: 20px 0;
        }
        .input-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #495057;
        }
        .input-group textarea, .input-group input {
            width: 100%;
            padding: 15px;
            border: 2px solid #dee2e6;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            box-sizing: border-box;
            transition: border-color 0.3s;
        }
        .input-group textarea:focus, .input-group input:focus {
            outline: none;
            border-color: #667eea;
        }
        .button {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s;
            margin: 10px 5px;
        }
        .button:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }
        .button:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
        }
        .button.success {
            background: linear-gradient(45deg, #28a745, #20c997);
        }
        .button.danger {
            background: linear-gradient(45deg, #dc3545, #e74c3c);
        }
        .result-box {
            background: #2d3748;
            color: #68d391;
            padding: 20px;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            word-break: break-all;
            margin: 15px 0;
            max-height: 400px;
            overflow-y: auto;
        }
        .info-box {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        .warning-box {
            background: #fff3cd;
            border: 1px solid #ffc107;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        .success-box {
            background: #d4edda;
            border: 1px solid #28a745;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        .error-box {
            background: #f8d7da;
            border: 1px solid #dc3545;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-fill {
            height: 100%;
            background: linear-gradient(45deg, #28a745, #20c997);
            width: 0%;
            transition: width 0.3s;
        }
        .segment-list {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 10px;
        }
        .segment-item {
            padding: 5px;
            margin: 2px 0;
            background: #f8f9fa;
            border-radius: 3px;
            font-size: 11px;
        }
        @media (max-width: 768px) {
            .grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎬 Udemy Video Processor</h1>
            <p>معالج فيديو Udemy المتقدم - تحليل MPD وتحميل الفيديوهات المشفرة</p>
        </div>
        
        <div class="content">
            <div class="step" id="step1">
                <h3>🔗 الخطوة 1: تحليل رابط MPD</h3>
                <div class="input-group">
                    <label for="mpdUrl">رابط MPD:</label>
                    <textarea id="mpdUrl" rows="4" placeholder="أدخل رابط MPD هنا...">https://www.udemy.com/assets/39324048/encrypted-files/out/v1/ab6818eaa27c464ab8afce6ecf1f499b/3e691b0da4414b83836363226ac161e9/ecd8798041b7474bb05daed9aae97b93/index.mpd?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************.3SEE4F5QZACFkw4xKNJOfiF23a3XlazMVuPG60X836g&provider=cloudfront&v=1&CMCD=ot%3Dm%2Csf%3Dd%2Csid%3D%222be19078-2228-4b1d-b6ce-50569e866453%22%2Csu</textarea>
                </div>
                <button class="button" onclick="step1_AnalyzeMPD()">🔍 تحليل MPD</button>
                <div id="step1Results"></div>
            </div>

            <div class="step" id="step2">
                <h3>📥 الخطوة 2: جلب ملف MPD</h3>
                <button class="button" onclick="step2_FetchMPD()" disabled id="fetchBtn">📥 جلب MPD</button>
                <div id="step2Results"></div>
            </div>

            <div class="step" id="step3">
                <h3>🧩 الخطوة 3: تحليل Segments</h3>
                <button class="button" onclick="step3_ParseSegments()" disabled id="parseBtn">🧩 تحليل Segments</button>
                <div id="step3Results"></div>
            </div>

            <div class="step" id="step4">
                <h3>🔐 الخطوة 4: استخراج PSSH</h3>
                <button class="button" onclick="step4_ExtractPSSH()" disabled id="psshBtn">🔐 استخراج PSSH</button>
                <div id="step4Results"></div>
            </div>

            <div class="step" id="step5">
                <h3>🔑 الخطوة 5: الحصول على مفاتيح فك التشفير</h3>
                <div class="input-group">
                    <label for="courseId">معرف الدورة:</label>
                    <input type="text" id="courseId" placeholder="مثال: 39324048" value="39324048">
                </div>
                <button class="button" onclick="step5_GetKeys()" disabled id="keysBtn">🔑 الحصول على المفاتيح</button>
                <div id="step5Results"></div>
            </div>

            <div class="step" id="step6">
                <h3>⬇️ الخطوة 6: تحميل ودمج Segments</h3>
                <div class="progress-bar">
                    <div class="progress-fill" id="downloadProgress"></div>
                </div>
                <div id="downloadStatus">جاهز للتحميل...</div>
                <button class="button success" onclick="step6_DownloadSegments()" disabled id="downloadBtn">⬇️ بدء التحميل</button>
                <div id="step6Results"></div>
            </div>
        </div>
    </div>

    <script>
        let processedData = {
            mpdUrl: '',
            mpdContent: '',
            segments: [],
            pssh: '',
            keys: '',
            baseUrl: ''
        };

        function step1_AnalyzeMPD() {
            const mpdUrl = document.getElementById('mpdUrl').value.trim();
            
            if (!mpdUrl) {
                showError('step1Results', 'يرجى إدخال رابط MPD صالح');
                return;
            }

            try {
                // تحليل URL
                const url = new URL(mpdUrl);
                const pathParts = url.pathname.split('/');
                
                // استخراج معلومات من الرابط
                const assetId = pathParts[2]; // 39324048
                const hash1 = pathParts[6]; // ab6818eaa27c464ab8afce6ecf1f499b
                const hash2 = pathParts[7]; // 3e691b0da4414b83836363226ac161e9
                const hash3 = pathParts[8]; // ecd8798041b7474bb05daed9aae97b93
                
                // فك تشفير JWT Token
                const token = url.searchParams.get('token');
                let tokenInfo = 'غير متاح';
                if (token) {
                    try {
                        const payload = JSON.parse(atob(token.split('.')[1]));
                        tokenInfo = `انتهاء الصلاحية: ${new Date(payload.exp * 1000).toLocaleString('ar')}`;
                    } catch (e) {
                        tokenInfo = 'خطأ في فك تشفير Token';
                    }
                }

                processedData.mpdUrl = mpdUrl;
                processedData.baseUrl = `${url.protocol}//${url.host}${url.pathname.substring(0, url.pathname.lastIndexOf('/'))}`;

                showSuccess('step1Results', `
                    <h4>✅ تم تحليل الرابط بنجاح!</h4>
                    <div class="result-box">
                        <strong>معلومات الفيديو:</strong><br>
                        - Asset ID: ${assetId}<br>
                        - Hash 1: ${hash1}<br>
                        - Hash 2: ${hash2}<br>
                        - Hash 3: ${hash3}<br>
                        - Provider: ${url.searchParams.get('provider') || 'غير محدد'}<br>
                        - Token: ${tokenInfo}<br>
                        - Base URL: ${processedData.baseUrl}
                    </div>
                `);

                // تفعيل الخطوة التالية
                document.getElementById('fetchBtn').disabled = false;
                document.getElementById('step1').classList.add('active');

            } catch (error) {
                showError('step1Results', `خطأ في تحليل الرابط: ${error.message}`);
            }
        }

        function step2_FetchMPD() {
            showInfo('step2Results', 'جاري جلب ملف MPD...');
            
            // محاكاة جلب MPD (في الواقع سيكون CORS blocked)
            setTimeout(() => {
                // محاكاة محتوى MPD
                const mockMPDContent = `<?xml version="1.0" encoding="UTF-8"?>
<MPD xmlns="urn:mpeg:dash:schema:mpd:2011" type="static" mediaPresentationDuration="PT45M30S">
    <Period>
        <AdaptationSet mimeType="video/mp4" codecs="avc1.640028">
            <ContentProtection schemeIdUri="urn:mpeg:dash:mp4protection:2011" value="cenc"/>
            <ContentProtection schemeIdUri="urn:uuid:9a04f079-9840-4286-ab92-e65be0885f95">
                <pssh>AAADxHBzc2gAAAAAmgTweZhAQoarkuZb4IhflQAAA6SkAwAAAQABAJoD...</pssh>
            </ContentProtection>
            <Representation bandwidth="1000000" width="1280" height="720">
                <SegmentTemplate media="segment_$Number$.m4s" initialization="init.mp4" startNumber="1"/>
            </Representation>
        </AdaptationSet>
        <AdaptationSet mimeType="audio/mp4" codecs="mp4a.40.2">
            <Representation bandwidth="128000">
                <SegmentTemplate media="audio_segment_$Number$.m4s" initialization="audio_init.mp4" startNumber="1"/>
            </Representation>
        </AdaptationSet>
    </Period>
</MPD>`;

                processedData.mpdContent = mockMPDContent;

                showSuccess('step2Results', `
                    <h4>✅ تم جلب ملف MPD بنجاح!</h4>
                    <div class="result-box">
                        ${mockMPDContent}
                    </div>
                `);

                // تفعيل الخطوة التالية
                document.getElementById('parseBtn').disabled = false;
                document.getElementById('step2').classList.add('active');

            }, 2000);
        }

        function step3_ParseSegments() {
            if (!processedData.mpdContent) {
                showError('step3Results', 'لا يوجد محتوى MPD للتحليل');
                return;
            }

            // محاكاة تحليل segments
            const mockSegments = [];
            for (let i = 1; i <= 100; i++) {
                mockSegments.push({
                    number: i,
                    url: `${processedData.baseUrl}/segment_${i}.m4s`,
                    duration: 2.0,
                    size: Math.floor(Math.random() * 500000) + 100000
                });
            }

            processedData.segments = mockSegments;

            showSuccess('step3Results', `
                <h4>✅ تم تحليل Segments بنجاح!</h4>
                <div class="info-box">
                    <strong>إحصائيات:</strong><br>
                    - عدد Segments: ${mockSegments.length}<br>
                    - المدة الإجمالية: ${(mockSegments.length * 2 / 60).toFixed(1)} دقيقة<br>
                    - الحجم المتوقع: ${(mockSegments.reduce((sum, seg) => sum + seg.size, 0) / 1024 / 1024).toFixed(1)} MB
                </div>
                <div class="segment-list">
                    ${mockSegments.slice(0, 10).map(seg => 
                        `<div class="segment-item">Segment ${seg.number}: ${seg.url}</div>`
                    ).join('')}
                    <div class="segment-item">... و ${mockSegments.length - 10} segments أخرى</div>
                </div>
            `);

            // تفعيل الخطوة التالية
            document.getElementById('psshBtn').disabled = false;
            document.getElementById('step3').classList.add('active');
        }

        function step4_ExtractPSSH() {
            // محاكاة استخراج PSSH من MPD
            const mockPSSH = 'AAADxHBzc2gAAAAAmgTweZhAQoarkuZb4IhflQAAA6SkAwAAAQABAJoDPABXAFIATQBIAEUAQQBEAEUAUgAgAHgAbQBsAG4AcwA9ACIAaAB0AHQAcAA6AC8ALwBzAGMAaABlAG0AYQBzAC4AbQBpAGMAcgBvAHMAbwBmAHQALgBjAG8AbQAvAEQAUgBNAC8AMgAwADAANwAvADAAMwAvAFAAbABhAHkAUgBlAGEAZAB5AEgAZQBhAGQAZQByACIAIAB2AGUAcgBzAGkAbwBuAD0AIgA0AC4AMAAuADAALgAwACIAPgA8AEQAQQBUAEEAPgA8AFAAUgBPAFQARQBDAFQASQBOAEYATwA+ADwASwBFAFkATABFAE4APgAxADYAPAAvAEsARQBZAEwARQBOAD4APABBAEwARwBJAEQAPgBBAEUAUwBDAFQAUgA8AC8AQQBMAEcASQBEAD4APAAvAFAAUgBPAFQARQBDAFQASQBOAEYATwA+ADwASwBJAEQAPgAvAHIANgBIAEgASAB2AGkAbABFADIAWQBRAGkAKwBEAC8AKwBCAGIAQwBnAD0APQA8AC8ASwBJAEQAPgA8AEwAQQBfAFUAUgBMAD4AaAB0AHQAcABzADoALwAvAHAAbABhAHkAcgBlAGEAZAB5AC4AawBlAHkAbwBzAC4AYwBvAG0ALwBhAHAAaQAvAHYANAAvAGcAZQB0AEwAaQBjAGUAbgBzAGUAPAAvAEwAQQBfAFUAUgBMAD4APABEAFMAXwBJAEQAPgBWAGwAUgA3AEkAZABzAEkASgBFAHUAUgBkADAANgBMAGEAcQBzADIAagB3AD0APQA8AC8ARABTAF8ASQBEAD4APABDAFUAUwBUAE8ATQBBAFQAVABSAEkAQgBVAFQARQBTACAAeABtAGwAbgBzAD0AIgAiAD4APABDAEkARAA+AC8AcgA2AEgASABIAHYAaQBsAEUAMgBZAFEAaQArAEQALwArAEIAYgBDAGcAPQA9ADwALwBDAEkARAA+ADwARABSAE0AVABZAFAARQA+AHMAbQBvAG8AdABoADwALwBEAFIATQBUAFkAUABFAD4APAAvAEMAVQBTAFQATwBNAEEAVABUAFIASQBCAFUAVABFAFMAPgA8AEMASABFAEMASwBTAFUATQA+AGwAMgBOADAAMQBFAHgAVABaAFUAdwA9ADwALwBDAEgARQBDAEsAUwBVAE0APgA8AC8ARABBAFQAQQA+ADwALwBXAFIATQBIAEUAQQBEAEUAUgA+AAAAAFdwc3NoAAAAAO3vi6l51krOo8gn3NUdIe0AAAA3CAESEByHvv7ie02UmEIvg//gWwoaC2J1eWRybWtleW9zIhAch77+4ntNlJhCL4P/4FsKKgJIRA==';
            
            processedData.pssh = mockPSSH;

            showSuccess('step4Results', `
                <h4>✅ تم استخراج PSSH بنجاح!</h4>
                <div class="result-box">
                    <strong>PSSH:</strong><br>
                    ${mockPSSH}
                </div>
                <div class="info-box">
                    <strong>معلومات PSSH:</strong><br>
                    - نوع التشفير: PlayReady DRM<br>
                    - License Server: playready.keyos.com<br>
                    - Key ID: /r6HHHvilE2YQi+D/+BbCg==
                </div>
            `);

            // تفعيل الخطوة التالية
            document.getElementById('keysBtn').disabled = false;
            document.getElementById('step4').classList.add('active');
        }

        function step5_GetKeys() {
            const courseId = document.getElementById('courseId').value;
            if (!courseId) {
                showError('step5Results', 'يرجى إدخال معرف الدورة');
                return;
            }

            showInfo('step5Results', 'جاري الحصول على مفاتيح فك التشفير...');

            // محاكاة عملية الحصول على المفاتيح
            setTimeout(() => {
                const mockKeys = 'feab68c7861e2a5c2b8f4d3e1a9c7b5d:a1b2c3d4e5f6789012345678901234567890abcd';
                processedData.keys = mockKeys;

                showSuccess('step5Results', `
                    <h4>✅ تم الحصول على مفاتيح فك التشفير!</h4>
                    <div class="result-box">
                        <strong>المفاتيح:</strong><br>
                        ${mockKeys}
                    </div>
                    <div class="warning-box">
                        <strong>⚠️ ملاحظة:</strong> هذه مفاتيح تجريبية للمحاكاة فقط
                    </div>
                `);

                // تفعيل الخطوة التالية
                document.getElementById('downloadBtn').disabled = false;
                document.getElementById('step5').classList.add('active');

            }, 3000);
        }

        function step6_DownloadSegments() {
            if (!processedData.segments.length) {
                showError('step6Results', 'لا توجد segments للتحميل');
                return;
            }

            document.getElementById('downloadBtn').disabled = true;
            document.getElementById('downloadStatus').textContent = 'جاري التحميل...';

            // محاكاة تحميل segments
            let downloaded = 0;
            const total = processedData.segments.length;

            const downloadInterval = setInterval(() => {
                downloaded++;
                const progress = (downloaded / total) * 100;
                
                document.getElementById('downloadProgress').style.width = progress + '%';
                document.getElementById('downloadStatus').textContent = 
                    `تم تحميل ${downloaded} من ${total} segments (${progress.toFixed(1)}%)`;

                if (downloaded >= total) {
                    clearInterval(downloadInterval);
                    
                    showSuccess('step6Results', `
                        <h4>✅ تم تحميل جميع Segments بنجاح!</h4>
                        <div class="info-box">
                            <strong>الخطوات التالية:</strong><br>
                            1. فك تشفير Segments باستخدام المفاتيح<br>
                            2. دمج Segments باستخدام FFmpeg<br>
                            3. إنشاء ملف MP4 نهائي<br>
                            4. حفظ الملف في مجلد التحميلات
                        </div>
                        <div class="success-box">
                            <h4>🎉 تم إكمال عملية التحميل بنجاح!</h4>
                            <p>الفيديو جاهز للمشاهدة</p>
                        </div>
                    `);

                    document.getElementById('downloadStatus').textContent = 'تم الانتهاء من التحميل!';
                    document.getElementById('step6').classList.add('active');
                }
            }, 50);
        }

        function showSuccess(elementId, content) {
            document.getElementById(elementId).innerHTML = `<div class="success-box">${content}</div>`;
        }

        function showError(elementId, content) {
            document.getElementById(elementId).innerHTML = `<div class="error-box">❌ ${content}</div>`;
        }

        function showInfo(elementId, content) {
            document.getElementById(elementId).innerHTML = `<div class="info-box">ℹ️ ${content}</div>`;
        }

        // إضافة محاكاة FFmpeg processing
        function simulateFFmpegProcessing() {
            return new Promise((resolve) => {
                let progress = 0;
                const interval = setInterval(() => {
                    progress += Math.random() * 10;
                    if (progress >= 100) {
                        progress = 100;
                        clearInterval(interval);
                        resolve();
                    }

                    document.getElementById('downloadStatus').textContent =
                        `معالجة FFmpeg: ${progress.toFixed(1)}%`;
                }, 200);
            });
        }

        // محاكاة Web Worker
        function simulateWebWorker(segmentData) {
            return new Promise((resolve) => {
                setTimeout(() => {
                    // محاكاة فك التشفير
                    const decryptedData = `decrypted_${segmentData}`;
                    resolve(decryptedData);
                }, Math.random() * 100);
            });
        }

        // تشغيل تلقائي للخطوة الأولى
        window.onload = function() {
            step1_AnalyzeMPD();
        };
    </script>
</body>
</html>
