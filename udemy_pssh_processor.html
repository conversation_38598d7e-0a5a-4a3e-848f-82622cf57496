<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Udemy PSSH Processor - معالج PSSH لـ Udemy</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            color: #333;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 40px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .content {
            padding: 40px;
        }
        .step {
            background: #f8f9fa;
            margin: 20px 0;
            padding: 25px;
            border-radius: 15px;
            border-left: 5px solid #667eea;
        }
        .step h3 {
            color: #495057;
            margin-top: 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .input-group {
            margin: 20px 0;
        }
        .input-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #495057;
        }
        .input-group textarea, .input-group input {
            width: 100%;
            padding: 15px;
            border: 2px solid #dee2e6;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            box-sizing: border-box;
            transition: border-color 0.3s;
        }
        .input-group textarea:focus, .input-group input:focus {
            outline: none;
            border-color: #667eea;
        }
        .button {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s;
            margin: 10px 5px;
        }
        .button:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }
        .button.secondary {
            background: linear-gradient(45deg, #6c757d, #495057);
        }
        .result-box {
            background: #2d3748;
            color: #68d391;
            padding: 20px;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            word-break: break-all;
            margin: 15px 0;
            max-height: 300px;
            overflow-y: auto;
        }
        .info-box {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        .warning-box {
            background: #fff3cd;
            border: 1px solid #ffc107;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        .success-box {
            background: #d4edda;
            border: 1px solid #28a745;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        .error-box {
            background: #f8d7da;
            border: 1px solid #dc3545;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        @media (max-width: 768px) {
            .grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔐 Udemy PSSH Processor</h1>
            <p>محاكاة عملية معالجة PSSH في إضافة UdemyFetcher</p>
        </div>
        
        <div class="content">
            <div class="step">
                <h3>🎯 الخطوة 1: إدخال PSSH</h3>
                <div class="input-group">
                    <label for="psshInput">PSSH (Base64):</label>
                    <textarea id="psshInput" rows="6" placeholder="أدخل PSSH هنا...">AAADxHBzc2gAAAAAmgTweZhAQoarkuZb4IhflQAAA6SkAwAAAQABAJoDPABXAFIATQBIAEUAQQBEAEUAUgAgAHgAbQBsAG4AcwA9ACIAaAB0AHQAcAA6AC8ALwBzAGMAaABlAG0AYQBzAC4AbQBpAGMAcgBvAHMAbwBmAHQALgBjAG8AbQAvAEQAUgBNAC8AMgAwADAANwAvADAAMwAvAFAAbABhAHkAUgBlAGEAZAB5AEgAZQBhAGQAZQByACIAIAB2AGUAcgBzAGkAbwBuAD0AIgA0AC4AMAAuADAALgAwACIAPgA8AEQAQQBUAEEAPgA8AFAAUgBPAFQARQBDAFQASQBOAEYATwA+ADwASwBFAFkATABFAE4APgAxADYAPAAvAEsARQBZAEwARQBOAD4APABBAEwARwBJAEQAPgBBAEUAUwBDAFQAUgA8AC8AQQBMAEcASQBEAD4APAAvAFAAUgBPAFQARQBDAFQASQBOAEYATwA+ADwASwBJAEQAPgAvAHIANgBIAEgASAB2AGkAbABFADIAWQBRAGkAKwBEAC8AKwBCAGIAQwBnAD0APQA8AC8ASwBJAEQAPgA8AEwAQQBfAFUAUgBMAD4AaAB0AHQAcABzADoALwAvAHAAbABhAHkAcgBlAGEAZAB5AC4AawBlAHkAbwBzAC4AYwBvAG0ALwBhAHAAaQAvAHYANAAvAGcAZQB0AEwAaQBjAGUAbgBzAGUAPAAvAEwAQQBfAFUAUgBMAD4APABEAFMAXwBJAEQAPgBWAGwAUgA3AEkAZABzAEkASgBFAHUAUgBkADAANgBMAGEAcQBzADIAagB3AD0APQA8AC8ARABTAF8ASQBEAD4APABDAFUAUwBUAE8ATQBBAFQAVABSAEkAQgBVAFQARQBTACAAeABtAGwAbgBzAD0AIgAiAD4APABDAEkARAA+AC8AcgA2AEgASABIAHYAaQBsAEUAMgBZAFEAaQArAEQALwArAEIAYgBDAGcAPQA9ADwALwBDAEkARAA+ADwARABSAE0AVABZAFAARQA+AHMAbQBvAG8AdABoADwALwBEAFIATQBUAFkAUABFAD4APAAvAEMAVQBTAFQATwBNAEEAVABUAFIASQBCAFUAVABFAFMAPgA8AEMASABFAEMASwBTAFUATQA+AGwAMgBOADAAMQBFAHgAVABaAFUAdwA9ADwALwBDAEgARQBDAEsAUwBVAE0APgA8AC8ARABBAFQAQQA+ADwALwBXAFIATQBIAEUAQQBEAEUAUgA+AAAAAFdwc3NoAAAAAO3vi6l51krOo8gn3NUdIe0AAAA3CAESEByHvv7ie02UmEIvg//gWwoaC2J1eWRybWtleW9zIhAch77+4ntNlJhCL4P/4FsKKgJIRA==</textarea>
                </div>
                <button class="button" onclick="step1_ProcessPSSH()">🔍 معالجة PSSH</button>
            </div>

            <div class="step">
                <h3>⚡ الخطوة 2: إرسال للخادم (Pre-Stage)</h3>
                <div class="input-group">
                    <label for="courseId">معرف الدورة:</label>
                    <input type="text" id="courseId" placeholder="مثال: 123456" value="123456">
                </div>
                <button class="button" onclick="step2_SendToServer()">📤 إرسال للخادم</button>
                <button class="button secondary" onclick="simulateServerResponse()">🎭 محاكاة رد الخادم</button>
            </div>

            <div class="step">
                <h3>🔑 الخطوة 3: معالجة الاستجابة</h3>
                <div class="input-group">
                    <label for="sessionId">Session ID:</label>
                    <input type="text" id="sessionId" placeholder="سيتم ملؤه تلقائياً..." readonly>
                </div>
                <div class="input-group">
                    <label for="challenge">Challenge:</label>
                    <textarea id="challenge" rows="3" placeholder="سيتم ملؤه تلقائياً..." readonly></textarea>
                </div>
                <button class="button" onclick="step3_ProcessChallenge()">⚙️ معالجة Challenge</button>
            </div>

            <div class="step">
                <h3>🎯 الخطوة 4: الحصول على المفاتيح</h3>
                <div class="input-group">
                    <label for="licenseResponse">License Response:</label>
                    <textarea id="licenseResponse" rows="3" placeholder="سيتم ملؤه تلقائياً..." readonly></textarea>
                </div>
                <button class="button" onclick="step4_GetKeys()">🔓 استخراج المفاتيح</button>
            </div>

            <div id="results" style="display: none;">
                <div class="step">
                    <h3>📊 النتائج النهائية</h3>
                    <div id="finalResults"></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // نسخ الدوال من الإضافة
        const fromHexString_dld = hexString => Uint8Array.from(hexString.match(/.{1,2}/g).map(byte => parseInt(byte, 16)));
        const toHexString_dld = bytes => bytes.reduce((str, byte) => str + byte.toString(16).padStart(2, '0'), '');
        const b64ToHex_dld = b64 => [...atob(b64)].map(c=> c.charCodeAt(0).toString(16).padStart(2,0)).join``;

        let processedData = {};

        function step1_ProcessPSSH() {
            const psshInput = document.getElementById('psshInput').value.trim();
            
            if (!psshInput) {
                showError('يرجى إدخال PSSH صالح');
                return;
            }

            try {
                // فك تشفير Base64
                const binaryData = atob(psshInput);
                const uint8Array = new Uint8Array(binaryData.length);
                for (let i = 0; i < binaryData.length; i++) {
                    uint8Array[i] = binaryData.charCodeAt(i);
                }

                // تحليل PSSH
                const psshInfo = analyzePSSH(uint8Array);
                processedData.pssh = psshInput;
                processedData.psshInfo = psshInfo;

                showSuccess('تم معالجة PSSH بنجاح!', `
                    <div class="result-box">
                        <strong>معلومات PSSH:</strong><br>
                        - حجم البيانات: ${uint8Array.length} بايت<br>
                        - System ID: ${psshInfo.systemId || 'غير محدد'}<br>
                        - License URL: ${psshInfo.licenseUrl || 'غير محدد'}<br>
                        - Key ID: ${psshInfo.keyId || 'غير محدد'}
                    </div>
                `);

            } catch (error) {
                showError(`خطأ في معالجة PSSH: ${error.message}`);
            }
        }

        function step2_SendToServer() {
            if (!processedData.pssh) {
                showError('يرجى معالجة PSSH أولاً');
                return;
            }

            const courseId = document.getElementById('courseId').value;
            if (!courseId) {
                showError('يرجى إدخال معرف الدورة');
                return;
            }

            // محاكاة إرسال للخادم
            showInfo('جاري الإرسال للخادم...', `
                <div class="result-box">
                    POST https://www.bestaddons.store/ytbdserver/downloader/drmKey/ude/getSecretKeyPre<br>
                    Body: pssh=${processedData.pssh.substring(0, 50)}...
                </div>
            `);

            // محاكاة تأخير الشبكة
            setTimeout(() => {
                showSuccess('تم الإرسال بنجاح!', 'انتظار رد الخادم...');
            }, 1000);
        }

        function simulateServerResponse() {
            // محاكاة رد الخادم
            const mockSessionId = 'sess_' + Math.random().toString(36).substr(2, 9);
            const mockChallenge = btoa('mock_challenge_data_' + Date.now());

            document.getElementById('sessionId').value = mockSessionId;
            document.getElementById('challenge').value = mockChallenge;

            processedData.sessionId = mockSessionId;
            processedData.challenge = mockChallenge;

            showSuccess('تم استلام رد الخادم!', `
                <div class="result-box">
                    Session ID: ${mockSessionId}<br>
                    Challenge: ${mockChallenge.substring(0, 50)}...
                </div>
            `);
        }

        function step3_ProcessChallenge() {
            if (!processedData.challenge) {
                showError('يرجى الحصول على Challenge أولاً');
                return;
            }

            // محاكاة معالجة Challenge
            const mockLicenseResponse = btoa(JSON.stringify({
                keys: [
                    {
                        kid: b64ToHex_dld(btoa('mock_key_id_1')),
                        k: b64ToHex_dld(btoa('mock_key_value_1'))
                    }
                ]
            }));

            document.getElementById('licenseResponse').value = mockLicenseResponse;
            processedData.licenseResponse = mockLicenseResponse;

            showSuccess('تم معالجة Challenge!', `
                <div class="result-box">
                    License Response: ${mockLicenseResponse.substring(0, 100)}...
                </div>
            `);
        }

        function step4_GetKeys() {
            if (!processedData.licenseResponse) {
                showError('يرجى معالجة Challenge أولاً');
                return;
            }

            try {
                // محاكاة استخراج المفاتيح
                const mockKeys = 'feab68c7861e2a5c2b8f4d3e1a9c7b5d:a1b2c3d4e5f6789012345678901234567890abcd';
                
                processedData.finalKeys = mockKeys;

                // عرض النتائج النهائية
                document.getElementById('results').style.display = 'block';
                document.getElementById('finalResults').innerHTML = `
                    <div class="success-box">
                        <h4>✅ تم الحصول على مفاتيح فك التشفير بنجاح!</h4>
                        <div class="result-box">
                            <strong>المفاتيح النهائية:</strong><br>
                            ${mockKeys}
                        </div>
                    </div>
                    
                    <div class="info-box">
                        <h4>📋 ملخص العملية:</h4>
                        <ul>
                            <li><strong>PSSH:</strong> تم معالجته بنجاح</li>
                            <li><strong>Session ID:</strong> ${processedData.sessionId}</li>
                            <li><strong>Challenge:</strong> تم معالجته</li>
                            <li><strong>المفاتيح:</strong> تم استخراجها</li>
                        </ul>
                    </div>
                    
                    <div class="warning-box">
                        <h4>⚠️ ملاحظة مهمة:</h4>
                        <p>هذه محاكاة لعملية فك التشفير. في الواقع، تتطلب العملية:</p>
                        <ul>
                            <li>اشتراك صالح في Udemy</li>
                            <li>خادم DRM متخصص</li>
                            <li>مكتبات WebAssembly لفك التشفير</li>
                        </ul>
                    </div>
                `;

                // التمرير للنتائج
                document.getElementById('results').scrollIntoView({ behavior: 'smooth' });

            } catch (error) {
                showError(`خطأ في استخراج المفاتيح: ${error.message}`);
            }
        }

        function analyzePSSH(uint8Array) {
            const result = {};
            const hexString = toHexString_dld(uint8Array);
            
            // البحث عن PSSH header
            const psshIndex = hexString.indexOf('70737368');
            if (psshIndex !== -1) {
                result.psshFound = true;
                
                // استخراج System ID
                const systemIdStart = (psshIndex + 16) / 2;
                const systemIdBytes = uint8Array.slice(systemIdStart, systemIdStart + 16);
                result.systemId = toHexString_dld(systemIdBytes);
            }
            
            // استخراج النص
            let extractedText = '';
            for (let i = 0; i < uint8Array.length; i++) {
                const char = uint8Array[i];
                if (char >= 32 && char <= 126) {
                    extractedText += String.fromCharCode(char);
                }
            }
            
            // البحث عن URL
            const urlMatch = extractedText.match(/https?:\/\/[^\s<>]+/);
            if (urlMatch) {
                result.licenseUrl = urlMatch[0];
            }
            
            // البحث عن Key ID
            const keyIdMatch = hexString.match(/([0-9a-f]{32})/gi);
            if (keyIdMatch && keyIdMatch.length > 0) {
                result.keyId = keyIdMatch[0];
            }
            
            return result;
        }

        function showSuccess(title, content = '') {
            showMessage('success-box', `<h4>✅ ${title}</h4>${content}`);
        }

        function showError(title, content = '') {
            showMessage('error-box', `<h4>❌ ${title}</h4>${content}`);
        }

        function showInfo(title, content = '') {
            showMessage('info-box', `<h4>ℹ️ ${title}</h4>${content}`);
        }

        function showMessage(className, content) {
            const messageDiv = document.createElement('div');
            messageDiv.className = className;
            messageDiv.innerHTML = content;
            
            // إزالة الرسائل السابقة
            const existingMessages = document.querySelectorAll('.success-box, .error-box, .info-box, .warning-box');
            existingMessages.forEach(msg => {
                if (!msg.closest('#finalResults')) {
                    msg.remove();
                }
            });
            
            // إضافة الرسالة الجديدة
            document.querySelector('.content').appendChild(messageDiv);
            messageDiv.scrollIntoView({ behavior: 'smooth' });
            
            // إزالة الرسالة بعد 5 ثوان (إلا إذا كانت خطأ)
            if (!className.includes('error')) {
                setTimeout(() => {
                    if (messageDiv.parentNode) {
                        messageDiv.remove();
                    }
                }, 5000);
            }
        }

        // تشغيل الخطوة الأولى تلقائياً
        window.onload = function() {
            step1_ProcessPSSH();
        };
    </script>
</body>
</html>
