#!/usr/bin/env python3
"""
Setup and Run Script for Udemy Video Downloader
سكريبت التثبيت والتشغيل لمحمل فيديوهات Udemy
"""

import subprocess
import sys
import os
import platform

def check_python_version():
    """التحقق من إصدار Python"""
    if sys.version_info < (3, 7):
        print("❌ يتطلب Python 3.7 أو أحدث")
        print(f"الإصدار الحالي: {sys.version}")
        return False
    
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
    return True

def install_requirements():
    """تثبيت المكتبات المطلوبة"""
    print("📦 تثبيت المكتبات المطلوبة...")
    
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ])
        print("✅ تم تثبيت المكتبات بنجاح")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ فشل في تثبيت المكتبات: {e}")
        return False

def check_ffmpeg():
    """التحقق من وجود FFmpeg"""
    try:
        result = subprocess.run(['ffmpeg', '-version'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ FFmpeg متاح")
            return True
    except FileNotFoundError:
        pass
    
    print("⚠️ FFmpeg غير مثبت")
    print_ffmpeg_install_instructions()
    return False

def print_ffmpeg_install_instructions():
    """طباعة تعليمات تثبيت FFmpeg"""
    system = platform.system().lower()
    
    print("\n📋 تعليمات تثبيت FFmpeg:")
    
    if system == "windows":
        print("Windows:")
        print("1. تحميل من: https://ffmpeg.org/download.html")
        print("2. استخراج الملفات")
        print("3. إضافة مجلد bin إلى PATH")
        print("أو استخدام Chocolatey: choco install ffmpeg")
        
    elif system == "darwin":  # macOS
        print("macOS:")
        print("brew install ffmpeg")
        
    elif system == "linux":
        print("Linux:")
        print("Ubuntu/Debian: sudo apt install ffmpeg")
        print("CentOS/RHEL: sudo yum install ffmpeg")
        print("Arch: sudo pacman -S ffmpeg")

def create_downloads_folder():
    """إنشاء مجلد التحميلات"""
    downloads_dir = "downloads"
    os.makedirs(downloads_dir, exist_ok=True)
    print(f"📁 مجلد التحميلات: {os.path.abspath(downloads_dir)}")

def run_downloader():
    """تشغيل المحمل"""
    print("\n🚀 تشغيل محمل فيديوهات Udemy...")
    print("=" * 50)
    
    try:
        subprocess.run([sys.executable, "udemy_video_downloader.py"])
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف العملية بواسطة المستخدم")
    except Exception as e:
        print(f"❌ خطأ في تشغيل المحمل: {e}")

def main():
    """الدالة الرئيسية"""
    print("🎬 Udemy Video Downloader Setup")
    print("=" * 40)
    
    # التحقق من Python
    if not check_python_version():
        return
    
    # تثبيت المكتبات
    if not install_requirements():
        return
    
    # التحقق من FFmpeg
    ffmpeg_available = check_ffmpeg()
    
    # إنشاء مجلد التحميلات
    create_downloads_folder()
    
    print("\n✅ الإعداد مكتمل!")
    
    if not ffmpeg_available:
        print("⚠️ تحذير: FFmpeg غير متاح. سيتم تحميل segments فقط بدون دمج")
        response = input("هل تريد المتابعة؟ (y/n): ")
        if response.lower() != 'y':
            return
    
    # تشغيل المحمل
    run_downloader()

if __name__ == "__main__":
    main()
