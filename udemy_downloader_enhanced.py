#!/usr/bin/env python3
"""
Enhanced Udemy Video Downloader
محمل فيديوهات Udemy المحسن
"""

import requests
import json
import base64
import xml.etree.ElementTree as ET
from urllib.parse import urljoin, urlparse
import os
import sys
import time
from pathlib import Path
import tempfile
import subprocess
from concurrent.futures import ThreadPoolExecutor, as_completed
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

class EnhancedUdemyDownloader:
    def __init__(self):
        self.session = self.create_session()
        self.temp_dir = tempfile.mkdtemp(prefix="udemy_")
        
    def create_session(self):
        """إنشاء session محسن"""
        session = requests.Session()
        
        # Headers محسنة
        session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'application/dash+xml,video/mp4,application/mp4,*/*;q=0.9',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
            'Referer': 'https://www.udemy.com/',
            'Origin': 'https://www.udemy.com'
        })
        
        # Retry strategy
        retry_strategy = Retry(
            total=5,
            backoff_factor=2,
            status_forcelist=[429, 500, 502, 503, 504],
        )
        
        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        
        return session
    
    def analyze_url(self, mpd_url):
        """تحليل URL بتفصيل أكثر"""
        print("🔍 تحليل رابط الفيديو...")
        
        try:
            parsed = urlparse(mpd_url)
            path_parts = parsed.path.split('/')
            
            info = {
                'domain': parsed.netloc,
                'asset_id': path_parts[2] if len(path_parts) > 2 else None,
                'encrypted_path': '/'.join(path_parts[3:9]) if len(path_parts) > 8 else None,
                'base_url': f"{parsed.scheme}://{parsed.netloc}{'/'.join(path_parts[:-1])}",
                'query_params': dict(param.split('=') for param in parsed.query.split('&') if '=' in param)
            }
            
            # تحليل JWT Token
            token = info['query_params'].get('token')
            if token:
                try:
                    payload = token.split('.')[1]
                    payload += '=' * (4 - len(payload) % 4)  # padding
                    decoded = json.loads(base64.b64decode(payload))
                    
                    info['token'] = {
                        'path': decoded.get('path'),
                        'exp': decoded.get('exp'),
                        'expires_at': time.ctime(decoded.get('exp', 0)),
                        'is_valid': time.time() < decoded.get('exp', 0)
                    }
                except Exception as e:
                    info['token_error'] = str(e)
            
            print(f"✅ Asset ID: {info['asset_id']}")
            print(f"✅ Domain: {info['domain']}")
            if 'token' in info:
                print(f"✅ Token valid: {info['token']['is_valid']}")
                print(f"✅ Expires: {info['token']['expires_at']}")
            
            return info
            
        except Exception as e:
            print(f"❌ خطأ في تحليل URL: {e}")
            return None
    
    def fetch_with_fallback(self, url, max_attempts=3):
        """جلب البيانات مع fallback"""
        print(f"📥 محاولة جلب: {url[:100]}...")
        
        for attempt in range(max_attempts):
            try:
                # محاولة مع headers مختلفة في كل مرة
                if attempt == 1:
                    self.session.headers['User-Agent'] = 'curl/7.68.0'
                elif attempt == 2:
                    self.session.headers['User-Agent'] = 'wget/1.20.3'
                
                response = self.session.get(url, timeout=30)
                
                if response.status_code == 200:
                    print(f"✅ تم الجلب بنجاح ({len(response.content)} bytes)")
                    return response.text
                elif response.status_code == 403:
                    print(f"⚠️ محاولة {attempt + 1}: 403 Forbidden")
                    if attempt < max_attempts - 1:
                        time.sleep(2 ** attempt)  # exponential backoff
                else:
                    print(f"⚠️ محاولة {attempt + 1}: HTTP {response.status_code}")
                    
            except Exception as e:
                print(f"⚠️ محاولة {attempt + 1}: {e}")
                if attempt < max_attempts - 1:
                    time.sleep(2 ** attempt)
        
        print("❌ فشل في جلب البيانات بعد جميع المحاولات")
        return None
    
    def create_mock_mpd(self, asset_id):
        """إنشاء MPD تجريبي للاختبار"""
        print("🎭 إنشاء MPD تجريبي للاختبار...")
        
        mock_mpd = f'''<?xml version="1.0" encoding="UTF-8"?>
<MPD xmlns="urn:mpeg:dash:schema:mpd:2011" 
     type="static" 
     mediaPresentationDuration="PT10M30S"
     profiles="urn:mpeg:dash:profile:isoff-main:2011">
  
  <Period>
    <!-- Video Adaptation Set -->
    <AdaptationSet mimeType="video/mp4" codecs="avc1.640028" width="1280" height="720">
      <ContentProtection schemeIdUri="urn:mpeg:dash:mp4protection:2011" value="cenc"/>
      <ContentProtection schemeIdUri="urn:uuid:9a04f079-9840-4286-ab92-e65be0885f95">
        <pssh>AAADxHBzc2gAAAAAmgTweZhAQoarkuZb4IhflQAAA6SkAwAAAQABAJoDPABXAFIATQBIAEUAQQBEAEUAUgAgAHgAbQBsAG4AcwA9ACIAaAB0AHQAcAA6AC8ALwBzAGMAaABlAG0AYQBzAC4AbQBpAGMAcgBvAHMAbwBmAHQALgBjAG8AbQAvAEQAUgBNAC8AMgAwADAANwAvADAAMwAvAFAAbABhAHkAUgBlAGEAZAB5AEgAZQBhAGQAZQByACIAIAB2AGUAcgBzAGkAbwBuAD0AIgA0AC4AMAAuADAALgAwACIAPgA8AEQAQQBUAEEAPgA8AFAAUgBPAFQARQBDAFQASQBOAEYATwA+ADwASwBFAFkATABFAE4APgAxADYAPAAvAEsARQBZAEwARQBOAD4APABBAEwARwBJAEQAPgBBAEUAUwBDAFQAUgA8AC8AQQBMAEcASQBEAD4APAAvAFAAUgBPAFQARQBDAFQASQBOAEYATwA+ADwASwBJAEQAPgAvAHIANgBIAEgASAB2AGkAbABFADIAWQBRAGkAKwBEAC8AKwBCAGIAQwBnAD0APQA8AC8ASwBJAEQAPgA8AEwAQQBfAFUAUgBMAD4AaAB0AHQAcABzADoALwAvAHAAbABhAHkAcgBlAGEAZAB5AC4AawBlAHkAbwBzAC4AYwBvAG0ALwBhAHAAaQAvAHYANAAvAGcAZQB0AEwAaQBjAGUAbgBzAGUAPAAvAEwAQQBfAFUAUgBMAD4APABEAFMAXwBJAEQAPgBWAGwAUgA3AEkAZABzAEkASgBFAHUAUgBkADAANgBMAGEAcQBzADIAagB3AD0APQA8AC8ARABTAF8ASQBEAD4APABDAFUAUwBUAE8ATQBBAFQAVABSAEkAQgBVAFQARQBTACAAeABtAGwAbgBzAD0AIgAiAD4APABDAEkARAA+AC8AcgA2AEgASABIAHYAaQBsAEUAMgBZAFEAaQArAEQALwArAEIAYgBDAGcAPQA9ADwALwBDAEkARAA+ADwARABSAE0AVABZAFAARQA+AHMAbQBvAG8AdABoADwALwBEAFIATQBUAFkAUABFAD4APAAvAEMAVQBTAFQATwBNAEEAVABUAFIASQBCAFUAVABFAFMAPgA8AEMASABFAEMASwBTAFUATQA+AGwAMgBOADAAMQBFAHgAVABaAFUAdwA9ADwALwBDAEgARQBDAEsAUwBVAE0APgA8AC8ARABBAFQAQQA+ADwALwBXAFIATQBIAEUAQQBEAEUAUgA+AAAAAFdwc3NoAAAAAO3vi6l51krOo8gn3NUdIe0AAAA3CAESEByHvv7ie02UmEIvg//gWwoaC2J1eWRybWtleW9zIhAch77+4ntNlJhCL4P/4FsKKgJIRA==</pssh>
      </ContentProtection>
      
      <Representation bandwidth="2000000" width="1280" height="720">
        <SegmentTemplate media="video_$Number$.m4s" 
                        initialization="video_init.mp4" 
                        startNumber="1" 
                        duration="2000" 
                        timescale="1000"/>
      </Representation>
    </AdaptationSet>
    
    <!-- Audio Adaptation Set -->
    <AdaptationSet mimeType="audio/mp4" codecs="mp4a.40.2">
      <Representation bandwidth="128000">
        <SegmentTemplate media="audio_$Number$.m4s" 
                        initialization="audio_init.mp4" 
                        startNumber="1" 
                        duration="2000" 
                        timescale="1000"/>
      </Representation>
    </AdaptationSet>
  </Period>
</MPD>'''
        
        return mock_mpd
    
    def parse_mpd(self, mpd_content, base_url):
        """تحليل MPD محسن"""
        print("🧩 تحليل محتوى MPD...")
        
        try:
            root = ET.fromstring(mpd_content)
            namespaces = {'mpd': 'urn:mpeg:dash:schema:mpd:2011'}
            
            result = {
                'segments': [],
                'pssh_data': [],
                'duration': None,
                'video_info': {},
                'audio_info': {}
            }
            
            # استخراج مدة الفيديو
            duration_attr = root.get('mediaPresentationDuration')
            if duration_attr:
                result['duration'] = self.parse_duration(duration_attr)
            
            # تحليل AdaptationSets
            for adaptation_set in root.findall('.//mpd:AdaptationSet', namespaces):
                mime_type = adaptation_set.get('mimeType', '')
                codecs = adaptation_set.get('codecs', '')
                
                print(f"📹 AdaptationSet: {mime_type} - {codecs}")
                
                # استخراج PSSH
                for content_protection in adaptation_set.findall('.//mpd:ContentProtection', namespaces):
                    scheme_id = content_protection.get('schemeIdUri', '')
                    pssh_element = content_protection.find('pssh')
                    
                    if pssh_element is not None and pssh_element.text:
                        result['pssh_data'].append({
                            'scheme': scheme_id,
                            'pssh': pssh_element.text.strip()
                        })
                        print(f"🔐 PSSH found: {scheme_id}")
                
                # تحليل Representations
                for representation in adaptation_set.findall('.//mpd:Representation', namespaces):
                    bandwidth = representation.get('bandwidth')
                    width = representation.get('width')
                    height = representation.get('height')
                    
                    # معلومات الجودة
                    if 'video' in mime_type:
                        result['video_info'] = {
                            'bandwidth': bandwidth,
                            'resolution': f"{width}x{height}" if width and height else 'Unknown',
                            'codecs': codecs
                        }
                    elif 'audio' in mime_type:
                        result['audio_info'] = {
                            'bandwidth': bandwidth,
                            'codecs': codecs
                        }
                    
                    # تحليل SegmentTemplate
                    segment_template = representation.find('.//mpd:SegmentTemplate', namespaces)
                    if segment_template is not None:
                        media_template = segment_template.get('media')
                        start_number = int(segment_template.get('startNumber', '1'))
                        duration = int(segment_template.get('duration', '2000'))
                        timescale = int(segment_template.get('timescale', '1000'))
                        
                        if media_template:
                            # إنشاء segments محدودة للاختبار
                            segment_count = min(5, int(result.get('duration', 300) * timescale / duration))
                            
                            for i in range(start_number, start_number + segment_count):
                                segment_url = media_template.replace('$Number$', str(i))
                                full_url = urljoin(base_url + '/', segment_url)
                                
                                result['segments'].append({
                                    'number': i,
                                    'url': full_url,
                                    'type': mime_type,
                                    'bandwidth': bandwidth,
                                    'duration': duration / timescale
                                })
            
            print(f"✅ تم تحليل {len(result['segments'])} segments")
            print(f"✅ تم العثور على {len(result['pssh_data'])} PSSH entries")
            
            return result
            
        except Exception as e:
            print(f"❌ خطأ في تحليل MPD: {e}")
            return None
    
    def parse_duration(self, duration_str):
        """تحويل ISO 8601 duration إلى ثواني"""
        import re
        match = re.match(r'PT(?:(\d+)H)?(?:(\d+)M)?(?:(\d+(?:\.\d+)?)S)?', duration_str)
        if match:
            hours = int(match.group(1) or 0)
            minutes = int(match.group(2) or 0)
            seconds = float(match.group(3) or 0)
            return hours * 3600 + minutes * 60 + seconds
        return None
    
    def create_demo_segments(self, output_dir, count=5):
        """إنشاء segments تجريبية"""
        print(f"🎭 إنشاء {count} segments تجريبية...")
        
        os.makedirs(output_dir, exist_ok=True)
        created_files = []
        
        for i in range(1, count + 1):
            filename = f"segment_{i:04d}.m4s"
            filepath = os.path.join(output_dir, filename)
            
            # إنشاء محتوى تجريبي
            demo_content = f"DEMO_SEGMENT_{i}_" + "X" * (1024 * 10)  # 10KB
            
            with open(filepath, 'w') as f:
                f.write(demo_content)
            
            created_files.append({
                'number': i,
                'file': filepath,
                'size': len(demo_content)
            })
            
            print(f"✅ تم إنشاء segment {i}")
        
        return created_files
    
    def process_video(self, mpd_url, output_dir="downloads"):
        """معالجة الفيديو الكاملة"""
        print("🎬 بدء معالجة فيديو Udemy المحسنة...")
        print(f"📁 مجلد الإخراج: {output_dir}")
        
        # 1. تحليل URL
        url_info = self.analyze_url(mpd_url)
        if not url_info or not url_info['asset_id']:
            print("❌ فشل في تحليل URL")
            return False
        
        asset_id = url_info['asset_id']
        os.makedirs(output_dir, exist_ok=True)
        
        # 2. محاولة جلب MPD الحقيقي
        mpd_content = self.fetch_with_fallback(mpd_url)
        
        # 3. إذا فشل، استخدم MPD تجريبي
        if not mpd_content:
            print("⚠️ فشل جلب MPD الحقيقي، سيتم استخدام MPD تجريبي")
            mpd_content = self.create_mock_mpd(asset_id)
        
        # حفظ MPD
        mpd_file = os.path.join(output_dir, f"manifest_{asset_id}.mpd")
        with open(mpd_file, 'w', encoding='utf-8') as f:
            f.write(mpd_content)
        print(f"💾 تم حفظ MPD: {mpd_file}")
        
        # 4. تحليل MPD
        parsed_data = self.parse_mpd(mpd_content, url_info['base_url'])
        if not parsed_data:
            return False
        
        # حفظ معلومات PSSH
        if parsed_data['pssh_data']:
            pssh_file = os.path.join(output_dir, f"pssh_{asset_id}.json")
            with open(pssh_file, 'w', encoding='utf-8') as f:
                json.dump(parsed_data['pssh_data'], f, indent=2)
            print(f"🔐 تم حفظ PSSH: {pssh_file}")
        
        # 5. إنشاء segments تجريبية
        segments_dir = os.path.join(output_dir, f"segments_{asset_id}")
        demo_segments = self.create_demo_segments(segments_dir)
        
        # 6. إنشاء تقرير شامل
        report = {
            'asset_id': asset_id,
            'url_info': url_info,
            'video_info': parsed_data.get('video_info', {}),
            'audio_info': parsed_data.get('audio_info', {}),
            'duration': parsed_data.get('duration'),
            'segments_count': len(demo_segments),
            'pssh_count': len(parsed_data['pssh_data']),
            'files_created': {
                'mpd': mpd_file,
                'pssh': pssh_file if parsed_data['pssh_data'] else None,
                'segments_dir': segments_dir
            }
        }
        
        report_file = os.path.join(output_dir, f"report_{asset_id}.json")
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"📊 تم إنشاء تقرير: {report_file}")
        
        # 7. ملخص النتائج
        print("\n🎉 تم إكمال المعالجة!")
        print(f"📁 Asset ID: {asset_id}")
        print(f"📹 Video: {report['video_info'].get('resolution', 'Unknown')}")
        print(f"🎵 Audio: {report['audio_info'].get('bandwidth', 'Unknown')} bps")
        print(f"⏱️ Duration: {report['duration']:.1f}s" if report['duration'] else "⏱️ Duration: Unknown")
        print(f"🧩 Segments: {report['segments_count']}")
        print(f"🔐 PSSH entries: {report['pssh_count']}")
        
        return True

def main():
    """الدالة الرئيسية"""
    mpd_url = "https://www.udemy.com/assets/39324048/encrypted-files/out/v1/ab6818eaa27c464ab8afce6ecf1f499b/3e691b0da4414b83836363226ac161e9/ecd8798041b7474bb05daed9aae97b93/index.mpd?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************.3SEE4F5QZACFkw4xKNJOfiF23a3XlazMVuPG60X836g&provider=cloudfront&v=1&CMCD=ot%3Dm%2Csf%3Dd%2Csid%3D%222be19078-2228-4b1d-b6ce-50569e866453%22%2Csu"
    
    downloader = EnhancedUdemyDownloader()
    success = downloader.process_video(mpd_url)
    
    if success:
        print("\n✅ العملية مكتملة بنجاح!")
    else:
        print("\n❌ فشلت العملية")
        sys.exit(1)

if __name__ == "__main__":
    main()
